# Obsidian筆記優化器 - 完備性強化版

## PURPOSE
將任何文本內容轉換為標準化Obsidian筆記格式，確保結構完整、格式統一、標籤規範。本框架適用於GPT、Gemini、Claude等主流AI模型，具有強制性約束確保完全遵守規範。

## CORE_FRAMEWORK

### 🔒 強制性規範（不可違背）

#### A. 檔案命名規範（必須遵守）
**🔒 前綴選擇決策樹**（必須使用以下之一）：
- `概念-`：回答"是什麼"、"為什麼" | 關鍵詞：原理、理論、機制、系統、架構
- `指南-`：回答"如何做"、"怎麼操作" | 關鍵詞：步驟、方法、教學、操作、實作
- `筆記-`：一般知識整理、個人記錄 | 關鍵詞：整理、記錄、總結、學習筆記
- `資料-`：參考資料、數據整理 | 關鍵詞：對照表、詞彙表、統計、清單
- `分析-`：深度評論、現象解讀 | 關鍵詞：分析、評論、解讀、批判
- `備忘錄-`：個人管理、策略記錄
- `日記-`：個人經歷、時間記錄

**🔒 命名格式精確控制**：`前綴-主題核心概念與範圍描述`
- 最佳長度：12-18字（包含前綴）
- 最短：8字（前綴+核心概念）
- 最長：25字（前綴+詳細描述）
- 🚫 禁止使用括號、冒號等特殊符號
- 🔒 必須反映內容本質，核心概念優先

#### B. YAML前置資料（必須完整）
```yaml
---
title: [與檔名完全一致的標題]
created: [YYYY-MM-DD格式]
modified: [YYYY-MM-DD格式]
version: "[主版本.次版本，如1.0]"
tags: [必須包含四大類標籤]
aliases: [至少2個相關別名：同義詞、簡稱、相關術語]
---
```

**🔒 aliases生成邏輯**：
1. 核心概念同義詞：主要概念的不同表達方式
2. 常用簡稱：專業術語的縮寫或簡稱
3. 相關領域術語：相關但不同的表達方式
4. 不同語言表達：中英文對照（適用時）

**🔒 版本號標準格式**：
- 新文件：統一使用 "1.0"
- 小幅修改：遞增次版本號 "1.1", "1.2"
- 重大修改：遞增主版本號 "2.0", "3.0"
- 格式：必須使用引號包圍 "X.Y"

#### C. 標籤系統（強制四大類）
**🔒 主題標籤階層判斷規則**（1-3個，🚫禁止使用一級標籤）：
- 🔒 **技術相關** → `主題/技術/程式設計`、`主題/技術/AI工具`、`主題/技術/硬體工程`
- 🔒 **科學相關** → `主題/科學/物理`、`主題/科學/化學`、`主題/科學/生物`
- 🔒 **人文相關** → `主題/人文/歷史`、`主題/人文/哲學`、`主題/人文/語言`
- 🔒 **生活相關** → `主題/生活/烹飪`、`主題/生活/娛樂`、`主題/生活/消費`
- 🔒 **健康相關** → `主題/健康/醫療`、`主題/健康/營養`、`主題/健康/運動`

**🔒 類型標籤選擇決策樹**（必須1個，🚫禁止創造新類型）：
1. 內容主要解釋理論/原理？ → `類型/概念`
2. 內容提供操作步驟/教學？ → `類型/指南`
3. 內容為一般知識整理？ → `類型/筆記`
4. 內容為參考資料/數據？ → `類型/資料`
5. 內容為深度評論/解讀？ → `類型/分析`

**🔒 狀態標籤**（必須1個，統一用詞）：
- `狀態/草稿`、`狀態/進行中`、`狀態/已完成`

**🔒 備註標籤**（條件必須，0-2個）：
   - AI生成內容>50%：必須添加`備註/AI生成`
   - 時效性內容：必須添加`備註/時效性`（如最新硬體資訊、當前政策等）
   - 其他：`備註/個人系統`、`備註/學習資源`、`備註/待驗證`

#### D. 內容結構（必須包含）
1. **彙整者備註區**（必須存在）：
   ```markdown
   > [!note] 彙整者備註:
   > [初始為空，供用戶添加個人註解]
   ```

2. **導航系統**（按長度強制要求）：
   - 500字以上：必須有TL;DR摘要
   - 2500字以上：必須有章節樹導航

3. **章節樹格式**（統一使用格式A）：
   ```markdown
   ## 📋 章節導航
   - [[#主要章節一|主要章節一]]
       - [[#子章節1.1|子章節1.1]]
       - [[#子章節1.2|子章節1.2]]
   - [[#主要章節二|主要章節二]]
       - [[#子章節2.1|子章節2.1]]
       - [[#子章節2.2|子章節2.2]]
   ```

4. **參考資料章節**（條件必須）：
   - 當內容來源於影片、教材且有URL可獲得時，必須添加：
   ```markdown
   ## 📚 參考資料
   - [資料來源標題](URL) - 簡要描述
   - [影片標題](YouTube URL) - 課程/講座描述
   ```

### 📝 內容優化規則

#### 標題層級規範
- H1：僅用於檔案主標題
- H2：主要章節開始
- H3：子章節
- H4及以下：細節分類

#### 視覺增強要求
1. **表情符號使用**：
   - 主要章節：📋、📊、🔍、💡、⚙️、🎯
   - 特殊區塊：🔒（重要）、⚠️（警告）、💡（提示）

2. **Callout區塊**（適當使用）：
   - `> [!note]`：一般說明
   - `> [!tip]`：實用建議
   - `> [!warning]`：重要警告
   - `> [!important]`：關鍵信息

3. **格式化規則**：
   - 關鍵概念：**粗體**
   - 次要強調：*斜體*
   - 專業術語：首次出現時簡要解釋
   - 比較資訊：使用表格組織

#### 內容組織原則
1. **段落分割**：避免超過150字的長段落
2. **列表使用**：
   - 步驟流程：有序列表
   - 特徵要點：無序列表
   - 最多兩層嵌套

3. **引用處理**：
   - 原文引述：使用引用區塊`>`
   - 來源標註：明確標記出處
   - 個人觀點：與原文清晰區分

### 🚫 禁止事項（絕對不可違背）

1. **禁止創建不存在的連結**：
   - 不得假設其他檔案存在
   - 不得生成範例參考資料連結
   - 內部連結僅用於文件內章節導航

2. **禁止省略必要元素**：
   - YAML前置資料必須完整
   - 四大類標籤必須齊全（時效性標籤視內容而定）
   - 彙整者備註區必須存在
   - 參考資料章節（當有來源URL時）

3. **禁止格式不一致**：
   - 章節樹格式必須統一
   - 標籤命名必須規範
   - 前綴使用必須正確

## PROCESSING_GUIDELINES

### 🔒 完成前必須檢查清單
```markdown
□ 檔案名稱符合前綴-核心概念格式（12-18字最佳）
□ title與檔案名完全一致
□ 包含完整四大類標籤
□ 主題標籤使用二級階層（禁止一級標籤）
□ 類型標籤僅選擇一個標準類型
□ 狀態標籤使用標準用詞
□ aliases至少包含2個相關別名
□ 彙整者備註區塊存在
□ AI生成內容>50%時添加備註/AI生成標籤
□ 時效性內容添加備註/時效性標籤
```

### 🚫 絕對禁止事項
- 創造新的類型標籤
- 使用一級主題標籤（必須使用二級）
- title與檔案名不一致
- 缺少彙整者備註區塊
- 版本號不使用引號格式
- aliases少於2個

### 內容類型適配
1. **學術文獻**：保留引用格式，標記研究方法
2. **教學內容**：突出步驟流程，添加檢查清單
3. **技術文檔**：強調實作細節，提供故障排除
4. **個人筆記**：保持原始風格，增強結構化

### 品質檢查清單
執行前必須確認：
- [ ] 檔案命名符合前綴規範
- [ ] YAML前置資料完整
- [ ] 四大類標籤齊全且正確（包含時效性標籤如適用）
- [ ] 彙整者備註區存在
- [ ] 導航系統符合長度要求
- [ ] 參考資料章節（如有來源URL）
- [ ] 章節樹格式統一
- [ ] 無不存在的外部連結
- [ ] 視覺元素適當使用

## OUTPUT_SPECIFICATIONS
"請將以下內容轉換為標準化Obsidian筆記格式，嚴格遵守所有強制性規範，確保結構完整、格式統一、標籤規範：[PROMPT]"

## 版本控制
- Version: 2.0 (完備性強化版)
- 強化重點：增加強制性約束、統一格式規範、完善檢查機制
