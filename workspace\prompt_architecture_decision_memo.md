# Prompt Architecture Decision Memo

**Version**: 1.0 Final  
**Created**: 2025-06-25  
**Purpose**: 記錄prompt架構設計的最終決策與技術細節，避免已迭代方案的混淆

---

## 🎯 最終確立的架構標準

### **三層分離架構** (絕對指標)
```
META_EXECUTION_LAYER (🔧)     - 給AI的執行指令
SUB_GENERATION_LAYER (📝)     - 對生成內容的要求  
OUTPUT_SPECIFICATION_LAYER (🎯) - 最終輸出規範
```

### **適用策略**: 混合策略 (選項C)
- **Meta-prompt**: 必須使用三層架構
- **複雜prompt**: 具有絕對指標意義時使用三層架構
- **簡單prompt**: 使用基本架構 (PURPOSE + CORE_FRAMEWORK + OUTPUT_SPECIFICATIONS)

### **固有命名域與階級結構**
```
核心模組 (必須):
├── PURPOSE (始終保留)
├── 三層架構核心模組 (Meta/複雜prompt)
├── ADAPTIVE_PROTOCOLS (替換所有GUIDELINES)
└── OUTPUT_SPECIFICATIONS (最終執行指令)

選用模組:
└── QUALITY_ASSURANCE_PROTOCOLS
    ├── CHECKLISTS
    │   ├── META_QUALITY_CHECKLIST
    │   ├── SUB_VALIDATION_CHECKLIST
    │   └── OUTPUT_VALIDATION_CHECKLIST
    ├── PROVEN_EXAMPLES (不用REPOSITORY)
    └── ERROR_PREVENTION_RECHECK (不用MATRIX)
```

### **符號化標記系統**
- 🔧 = META_INSTRUCTION (給AI的執行指令)
- 📝 = SUB_REQUIREMENT (對內容的要求)
- 🎯 = OUTPUT_REQUIREMENT (輸出規範)
- 🔒 = 品質檢查
- 🚫 = 禁止事項

---

## 📚 架構演進歷程

### **已捨棄的方案** (避免混淆)
- ❌ `CORE_FRAMEWORK` + `PROCESSING_GUIDELINES` (早期基本架構)
- ❌ workspace中的6.0版本 (實驗失敗版本)
- ❌ `TEMPLATE_REPOSITORY` (改為PROVEN_EXAMPLES)
- ❌ `ERROR_PREVENTION_MATRIX` (改為ERROR_PREVENTION_RECHECK)

### **確立的標準** (checkpoint 16-25)
- ✅ 三層分離架構解決命名域混淆
- ✅ ADAPTIVE_PROTOCOLS替換所有GUIDELINES
- ✅ 固有模組種類限制
- ✅ 混合適用策略

---

## 🔧 技術實現細節

### **三層架構的核心模組**
```
META_EXECUTION_LAYER:
├── MODE_SELECTION_PROTOCOL
├── TASK_TYPE_RECOGNITION_ENGINE  
├── ROLE_IDENTIFICATION_ENGINE
├── COMPLEXITY_ADJUSTMENT_MECHANISM
└── PROCESS_CONSTRUCTION_ENGINE

SUB_GENERATION_LAYER:
├── SUB_TEMPLATE_STRUCTURE
├── VARIABLE_INTEGRATION_PROTOCOL
└── DOMAIN_SPECIFIC_ADAPTATIONS

OUTPUT_SPECIFICATION_LAYER:
├── OUTPUT_QUALITY_REQUIREMENTS
├── CROSS_DOMAIN_CONSISTENCY_PROTOCOL
└── OUTPUT_FORMAT_SPECIFICATIONS
```

### **品質保證機制**
```
QUALITY_ASSURANCE_PROTOCOLS:
├── CHECKLISTS (三層檢查清單)
├── PROVEN_EXAMPLES (驗證範例)
└── ERROR_PREVENTION_RECHECK (錯誤預防)
```

---

## 📋 版本控制標準

### **版本號統一標準**
- 以`資料-Prompts (new v2).md`為基準
- 舊版本在前，新版本在後
- 清除所有混亂版本號

### **文件組織標準**
- workspace保留偶數個文件（集合+說明）
- 每個prompt系列一對文件
- 避免版本混淆和重複

---

## 🚫 禁止事項

### **架構設計禁止**
- 🚫 不得使用已捨棄的GUIDELINES架構
- 🚫 不得混用不同版本的模組命名
- 🚫 不得在簡單prompt中強制使用三層架構
- 🚫 不得使用REPOSITORY、MATRIX等已替換的命名

### **版本管理禁止**
- 🚫 不得創建與標準版本號衝突的版本
- 🚫 不得保留已迭代的實驗版本
- 🚫 不得在文檔中反覆提及已捨棄方案

---

## ✅ 執行檢查清單

### **新prompt創建檢查**
□ 確認適用策略（三層 vs 基本架構）
□ 使用正確的固有模組命名
□ 應用ADAPTIVE_PROTOCOLS替換GUIDELINES
□ 符號化標記系統正確使用
□ 版本號與標準一致

### **現有prompt修正檢查**
□ 移除已捨棄架構的殘留
□ 統一版本號標準
□ 修正模組命名混淆
□ 確保架構一致性

---

**維護原則**: 本備忘錄為最終決策記錄，任何架構變更需更新此文檔  
**執行標準**: 所有新建prompt必須符合此架構標準  
**版本基準**: 以此備忘錄為準，避免參考已迭代版本
