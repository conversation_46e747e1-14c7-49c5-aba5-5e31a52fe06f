---
title: 資料-最新Prompt集合 v2
created: 2025-06-25
modified: 2025-06-25
version: "2.0"
tags: [主題/技術/AI, 類型/資料, 狀態/已完成, 備註/最新開發]
aliases: [Prompt Collection v2, 最新提示詞v2, AI提示詞集合v2]
---

# 資料-最新Prompt集合 v2

> [!note] 彙整者備註:
> v2版本整合了最新開發的4個prompt工具，並修正版本排序（舊版本在上，新版本在下）

本檔案收錄最新開發且具有實用價值的提示詞（prompts），按功能類型分類整理，並建立版本控制追蹤。

## 📋 章節導航

- **[[#PROMPT規格化類型]]**
	├─── [[#prompt_generalization_framework|提示詞標準化 (舊版)]]
	├─── [[#prompt_generating_framework|提示詞生成]]
	├─── [[#prompt_generalization_framework_1.0+|提示詞標準化 v1.0+]]
	└─── [[#prompt_generalization_framework_3.0|提示詞標準化 v3.0]]

- **[[#內容分析與處理類型]]**
	├─── [[#historical_political_analysis_framework|歷史/政治分析]]
	└─── [[#universal_media_analyzer|通用內容分析]]

- **[[#內容構建類型]]**
	├─── Obsidian筆記生成與最佳化
		├─── [[#obsidian_note_optimizer_simplified|簡易版 v1.0]]
		├─── [[#obsidian_note_optimizer|詳盡版 v1.0]]
		├─── [[#obsidian_note_optimizer_2.0|完備性強化版 v2.0]]
		├─── [[#universal_information_organizer|通用資訊組織引擎 v3.0]]
		└─── [[#obsidian_format_standardization_engine|Obsidian格式規範引擎 v3.0]]
	└─── [[#token_compression_framework|token壓縮框架 v2.0]]

- **[[#對話串處理類型]]**
	├─── [[#content_summarizing_organizer|對話串整理 v1.0]]
	└─── [[#memory_extraction_framework|記憶抽取框架 v2.0]]

- **[[#封存中-inactive]]**
	└─── [[#system_prompt]]

---

## PROMPT規格化類型

### prompt_generalization_framework
**Version Control:**
- Version: 1.0 (舊版)
- Created: 2025-06-25
- Purpose: 提供統一化的提示詞設計標準，確保結構清晰、命名一致且內容規範化
- Status: 已被v1.0+和v3.0版本取代

````
# prompt_generalization_framework

## PURPOSE
提供統一化的提示詞（prompt）設計標準，確保各類提示詞結構清晰、命名一致且內容規範化。本框架適用於創建高效能、可重複使用的提示詞，能跨平台應用且易於維護和改進。

## CORE_FRAMEWORK

### 基本結構標準
1. **必要模組**（所有提示詞必須包含）
   - `PURPOSE` - 提示詞目標與適用場景
   - `CORE_FRAMEWORK` - 核心功能架構
   - `OUTPUT_SPECIFICATIONS` - 執行指令與回應規範

2. **選用模組**（根據需求添加）
   - `PROCESSING_GUIDELINES` - 處理建議
   - `EXAMPLES` - 應用範例
   - `VERSION_CONTROL` - 版本資訊

### 命名規範
- 模組名稱使用英文大寫與下劃線
- 確保跨語言環境一致性

## OUTPUT_SPECIFICATIONS
"使用此框架標準化以下提示詞：[PROMPT]"
````

### prompt_generating_framework
**Version Control:**
- Version: 1.0
- Created: 2025-06-25
- Purpose: 自動生成符合標準的提示詞框架
- Status: 活躍使用中

````
# prompt_generating_framework

## PURPOSE
自動生成符合prompt_generalization_framework標準的提示詞，確保結構完整、功能明確且易於使用。

## CORE_FRAMEWORK

### 生成流程
1. **需求分析**
   - 識別提示詞類型和目標
   - 確定必要功能模組
   - 評估複雜度需求

2. **結構建立**
   - 應用標準化模組結構
   - 確保模組順序正確
   - 添加適當的擴展模組

3. **內容填充**
   - 編寫清晰的PURPOSE說明
   - 建立完整的CORE_FRAMEWORK
   - 設計有效的OUTPUT_SPECIFICATIONS

## OUTPUT_SPECIFICATIONS
"根據以下需求生成標準化提示詞：[需求描述]"
````

### prompt_generalization_framework_1.0+
**Version Control:**
- Version: 1.0+ (統合版本：基於1.0 > 2.3 > 2.0路徑，以2.3為基準)
- Created: 2025-06-25
- Purpose: 提供統一化的提示詞設計標準，確保結構清晰、命名一致且內容規範化
- Base: v2.3完備性設計，整合早期版本精華

````
# prompt_generalization_framework_1.0+

## PURPOSE
提供統一化的提示詞（prompt）設計標準，確保各類提示詞結構清晰、命名一致且內容規範化。本框架適用於創建高效能、可重複使用的提示詞，能跨平台應用且易於維護和改進。

## CORE_FRAMEWORK

### 模組分類標準
1. **核心模組**（確保提示詞基本功能的必要組件）
   - `PURPOSE` - 提示詞目標與適用場景的完整描述
   - `CORE_FRAMEWORK` - 提示詞的核心功能架構與方法論
   - `OUTPUT_SPECIFICATIONS` - 實際執行指令與回應規範

2. **擴展模組**（根據提示詞複雜度和特定需求選用）
   - `PROCESSING_GUIDELINES` - 特殊情境處理建議
   - `EXAMPLES` - 提示詞應用的範例展示
   - `VERSION_CONTROL` - 版本資訊與變更記錄
   - `USER_SUPPLEMENTARY` - 使用者增補資訊（臨時需求）

### 內容組織標準
1. **模組順序規範**
   - PURPOSE 必須位於首位
   - CORE_FRAMEWORK 置於第二位
   - 擴展模組根據相關性排序
   - USER_SUPPLEMENTARY 位於最末，便於使用者直接附加內容
   - OUTPUT_SPECIFICATIONS 放在倒數第二位

### 語言與格式標準
1. **模組名稱規範**
   - 統一使用英文大寫與下劃線（如：CORE_FRAMEWORK）
   - 確保跨語言環境中的一致識別性

## PROCESSING_GUIDELINES

### 提示詞類型調整指南
1. **分析型提示詞建議**
   - 強化CORE_FRAMEWORK中的分析方法論
   - 在PROCESSING_GUIDELINES中加入客觀性要求
   - OUTPUT_SPECIFICATIONS需明確分析報告格式

2. **創作型提示詞建議**
   - PURPOSE中明確創作風格與目標受眾
   - CORE_FRAMEWORK包含創意發想與結構規劃
   - 可在EXAMPLES中提供多樣化創作範例

3. **技術型提示詞建議**
   - CORE_FRAMEWORK需包含技術規範與限制條件
   - PROCESSING_GUIDELINES加入錯誤處理機制
   - OUTPUT_SPECIFICATIONS明確技術文檔格式

## OUTPUT_SPECIFICATIONS
"使用此增強版框架標準化以下提示詞，確保結構完整且功能明確：[PROMPT]"
````

### prompt_generalization_framework_4.0
**Version Control:**
- Version: 4.0 (最新版)
- Created: 2025-06-25
- Purpose: 基於V1+完備性設計整合V3核心決策的終極標準化框架
- Features: 完備指導體系、自適應協議、固有模組限制、零脈絡理解優化

````
# prompt_generalization_framework_4.0

## PURPOSE
本prompt是供AI參考的指示模板，用於標準化其他prompt的設計。提供統一化的提示詞設計標準，確保各類提示詞結構清晰、命名一致且內容規範化。本框架適用於創建高效能、可重複使用的提示詞，能跨平台應用且易於維護和改進。

## CORE_FRAMEWORK

### 模組分類標準
1. **核心模組**（確保提示詞基本功能的必要組件）
   - `PURPOSE` - 提示詞目標與適用場景的完整描述，必須明文說明"本prompt是供AI參考的指示模板"
   - `CORE_FRAMEWORK` - 提示詞的核心功能架構與方法論
   - `ADAPTIVE_PROTOCOLS` - 替換所有GUIDELINES，具備自適應處理機制
   - `OUTPUT_SPECIFICATIONS` - 使用自然語言的實際執行指令與回應規範

2. **選用模組**（根據提示詞複雜度和特定需求選用）
   - `QUALITY_ASSURANCE_PROTOCOLS` - 品質保證機制，包含TOKEN_EFFICIENCY_CHECK
   - `EXAMPLES` - 提示詞應用的範例展示
   - `USER_SUPPLEMENTARY` - 使用者增補資訊（內容留白，供快速增加條件）

### 內容組織標準
1. **模組順序規範**
   - PURPOSE 必須位於首位，明確說明AI參考用途
   - CORE_FRAMEWORK 置於第二位
   - ADAPTIVE_PROTOCOLS 替換所有GUIDELINES位置
   - 選用模組根據相關性排序
   - USER_SUPPLEMENTARY 位於倒數第二位，便於使用者直接附加內容
   - OUTPUT_SPECIFICATIONS 放在最末位

2. **結構層級規範**
   - 模組標題使用二級標題（##）
   - 主要分類使用三級標題（###）
   - 次級分類使用四級標題（####）
   - 避免過度細分，確保層級關係清晰

3. **內容呈現規範**
   - 連貫段落：用於概念解釋、背景說明及需完整理解的內容
   - 條列形式：用於步驟指引、要點總結及並列元素
   - 表格形式：用於比較分析、多維度數據呈現
   - 混合形式：重要概念用條列，說明部分用連貫段落

### 語言與格式標準
1. **模組名稱規範**
   - 統一使用英文大寫與下劃線（如：CORE_FRAMEWORK）
   - 確保跨語言環境中的一致識別性

2. **內容語言選擇規範**
   - 模組標題統一使用英文以維持跨語言識別性
   - 說明性內容可使用目標語言，但保持章節內語言一致性
   - 關鍵技術術語採用中英對照確保概念準確傳達

## ADAPTIVE_PROTOCOLS

### 提示詞類型自適應處理
1. **分析型提示詞**
   - 自動識別：包含"分析"、"評估"、"研究"、"比較"等關鍵詞
   - 自適應調整：強化CORE_FRAMEWORK中的分析方法論
   - 處理策略：在ADAPTIVE_PROTOCOLS中加入客觀性要求
   - 輸出規範：OUTPUT_SPECIFICATIONS需明確分析報告格式

2. **創作型提示詞**
   - 自動識別：包含"創作"、"設計"、"構思"、"創新"等關鍵詞
   - 自適應調整：PURPOSE中明確創作風格與目標受眾
   - 處理策略：CORE_FRAMEWORK包含創意發想與結構規劃
   - 輸出規範：可在EXAMPLES中提供多樣化創作範例

3. **技術型提示詞**
   - 自動識別：包含"開發"、"實現"、"配置"、"優化"等關鍵詞
   - 自適應調整：CORE_FRAMEWORK需包含技術規範與限制條件
   - 處理策略：ADAPTIVE_PROTOCOLS加入錯誤處理機制
   - 輸出規範：OUTPUT_SPECIFICATIONS明確技術文檔格式

### 複雜度自適應機制
1. **簡單提示詞**
   - 觸發條件：單一功能、明確目標、標準流程
   - 自動選擇：核心模組 + 基礎ADAPTIVE_PROTOCOLS
   - 處理原則：直接明確，避免過度複雜化

2. **中等複雜度提示詞**
   - 觸發條件：多重功能、部分模糊需求、需要判斷
   - 自動選擇：核心模組 + 完整ADAPTIVE_PROTOCOLS + 部分選用模組
   - 處理原則：分層處理，適度引導

3. **高複雜度提示詞**
   - 觸發條件：多層次目標、高度專業化、創新需求
   - 自動選擇：全套模組 + 強化QUALITY_ASSURANCE_PROTOCOLS
   - 處理原則：深度分析，全面覆蓋

### 用戶適應性指導
1. **新手用戶導向**
   - 識別標記：使用基礎術語、需求描述簡單、尋求指導
   - 自動調整：增加詳細說明、提供步驟指導、展示範例
   - 語言風格：友善引導、避免過度專業術語

2. **專業用戶導向**
   - 識別標記：使用專業術語、需求描述複雜、具備自主能力
   - 自動調整：精簡專業表達、聚焦核心要點、提供高效執行路徑
   - 語言風格：專業簡潔、直接有效

## QUALITY_ASSURANCE_PROTOCOLS

### 提示詞評估與優化指南
1. **功能完整性檢查**
   - 核心功能是否明確定義
   - 執行步驟是否邏輯清晰
   - 輸出要求是否具體明確
   - 邊界條件是否適當處理

2. **可用性評估標準**
   - 零脈絡測試：prompt在無額外說明下能獨立運作
   - 專業術語適度使用：平衡專業性與可理解性
   - 立竿見影效果保護：確保核心功能不受優化影響
   - TOKEN_EFFICIENCY_CHECK：監控token使用效率，平衡功能完整性與簡潔性

3. **一致性驗證機制**
   - 術語使用一致性：相同概念使用統一術語
   - 格式標準一致性：模組命名、結構層級保持統一
   - 邏輯流程一致性：處理流程、決策機制統一

### 常見問題預防
1. **避免過度工程化**
   - 不為精簡而移除有效功能
   - 保持模組數量在合理範圍
   - 確保每個模組都有明確作用

2. **確保實用性**
   - 提供具體可執行的指導
   - 避免過於抽象的概念描述
   - 包含必要的範例和說明

## USER_SUPPLEMENTARY
[使用者可在此快速增加特殊條件或需求]

## OUTPUT_SPECIFICATIONS
使用4.0版本框架標準化以下提示詞，應用完備指導體系和自適應協議：[PROMPT]
````

---

## 內容分析與處理類型

### historical_political_analysis_framework
**Version Control:**
- Version: 1.0
- Created: 2025-05
- Purpose: 專門分析歷史政治事件的框架

````
# historical_political_analysis_framework

## PURPOSE
專門用於分析歷史政治事件、政治制度演變、政治人物行為及其影響的綜合分析框架。

## CORE_FRAMEWORK

### 分析維度
1. **歷史背景分析**
   - 時代背景與社會環境
   - 經濟條件與社會結構
   - 文化思潮與意識形態

2. **政治行為分析**
   - 決策過程與動機
   - 權力結構與利益關係
   - 政治策略與手段

3. **影響評估**
   - 短期影響與長期後果
   - 對不同群體的影響
   - 歷史意義與現代啟示

## OUTPUT_SPECIFICATIONS
"對以下歷史政治事件進行多維度分析：[PROMPT]"
````

### universal_media_analyzer
**Version Control:**
- Version: 1.0
- Created: 2025-05
- Purpose: 通用媒體內容分析工具

````
# universal_media_analyzer

## PURPOSE
提供跨媒體類型的內容分析能力，適用於文字、影像、音頻等多種媒體形式的深度分析。

## CORE_FRAMEWORK

### 分析層次
1. **表層分析**
   - 基本信息提取
   - 格式與結構分析
   - 技術特徵識別

2. **內容分析**
   - 主題與論點識別
   - 情感傾向分析
   - 修辭手法分析

3. **深層分析**
   - 隱含意義挖掘
   - 文化背景解讀
   - 社會影響評估

## OUTPUT_SPECIFICATIONS
"對以下媒體內容進行全面分析：[PROMPT]"
````

---

## 內容構建類型

### obsidian_note_optimizer_simplified
**Version Control:**
- Version: 1.0 (簡易版)
- Created: 2025-05
- Purpose: Obsidian筆記的基礎優化工具

````
# obsidian_note_optimizer_simplified

## PURPOSE
提供Obsidian筆記的基礎優化功能，專注於核心格式標準化和結構改善。

## CORE_FRAMEWORK

### 基礎優化項目
1. **格式標準化**
   - YAML前置資料完整性
   - 標題層級規範化
   - 標籤系統統一

2. **結構優化**
   - 內容邏輯重組
   - 導航系統建立
   - 視覺增強應用

## OUTPUT_SPECIFICATIONS
"對以下內容進行Obsidian筆記基礎優化：[PROMPT]"
````

### obsidian_note_optimizer
**Version Control:**
- Version: 1.0 (詳盡版)
- Created: 2025-05
- Purpose: Obsidian筆記的完整優化工具

````
# obsidian_note_optimizer

## PURPOSE
提供Obsidian筆記的完整優化功能，包含格式標準化、內容組織、知識連接等全方位改善。

## CORE_FRAMEWORK

### 完整優化流程
1. **格式規範化**
   - 檔案命名標準
   - YAML完整配置
   - 四大類標籤系統

2. **內容深度優化**
   - 邏輯結構重組
   - 知識點關聯建立
   - 導航系統完善

3. **視覺與功能增強**
   - Callout區塊應用
   - 表格與列表優化
   - 內部連結建立

## OUTPUT_SPECIFICATIONS
"對以下內容進行Obsidian筆記完整優化：[PROMPT]"
````

### obsidian_note_optimizer_2.0
**Version Control:**
- Version: 2.0 (完備性強化版)
- Created: 2025-06
- Purpose: 基於使用經驗改進的強化版Obsidian筆記優化器

````
# obsidian_note_optimizer_2.0

## PURPOSE
基於實際使用經驗改進的強化版Obsidian筆記優化器，增強了規範性和資訊整理能力。

## CORE_FRAMEWORK

### 強化功能
1. **強制性規範**
   - 不可違背的格式要求
   - 嚴格的標籤分類系統
   - 必要元素檢查清單

2. **資訊整理增強**
   - 知識連接方法
   - 內容衝突處理
   - 場景適應策略

3. **品質保證機制**
   - 完成前檢查清單
   - 禁止事項明確化
   - 一致性驗證

## OUTPUT_SPECIFICATIONS
"使用2.0強化版對以下內容進行Obsidian筆記優化：[PROMPT]"
````

### universal_information_organizer
**Version Control:**
- Version: 3.0 (通用資訊組織引擎)
- Created: 2025-06-25
- Purpose: 通用資訊精煉與統合工具，提供深度組織和快速總結兩種模式
- Status: 最新開發，整合了information_integration和content_summarizing功能

````
# universal_information_organizer

## PURPOSE
通用資訊精煉與統合工具，提供深度組織模式（完整資訊統合）和快速總結模式（對話串整理）。適用於任何文本內容，不限特定格式。

## MODE_SELECTION
輸入特徵分析 → 模式選擇：
"對話|討論|會議|交流|聊天記錄" → SUMMARY_MODE
"文章|新聞|報告|論文|文檔" → ORGANIZATION_MODE
"快速|概要|簡要|總結" → SUMMARY_MODE
"深度|完整|詳細|統合" → ORGANIZATION_MODE

手動模式指定：
- ORGANIZATION_MODE：深度組織模式（完整資訊統合）
- SUMMARY_MODE：快速總結模式（對話串整理）
- AUTO_MODE：根據輸入特徵自動選擇

## CORE_FRAMEWORK

### ORGANIZATION_MODE功能
- 10種內容類型識別與處理
- 4種場景適應策略
- 內容衝突與重複處理
- 知識連接與關聯建立
- 內容品質提升策略

### SUMMARY_MODE功能
- 六步驟整理法
- 結構組織原則
- 內容深度控制
- 關聯性標註方法
- 互動式結尾

## OUTPUT_SPECIFICATIONS
ORGANIZATION_MODE輸出：
"使用深度組織模式對以下內容進行完整資訊統合：[PROMPT]"

SUMMARY_MODE輸出：
"使用快速總結模式對以下對話內容進行六步驟整理：[PROMPT]"

AUTO_MODE輸出：
"根據內容特徵自動選擇處理模式進行資訊組織：[PROMPT]"
````

### obsidian_format_standardization_engine
**Version Control:**
- Version: 3.0 (Obsidian格式規範引擎)
- Created: 2025-06-25
- Purpose: 專門負責Obsidian筆記格式標準化的工具
- Status: 最新開發，專注於格式規範與視覺增強

````
# obsidian_format_standardization_engine

## PURPOSE
專門負責Obsidian筆記格式標準化：檔案命名、YAML、四大類標籤、內容結構、視覺增強的完全規範化。

## CORE_FRAMEWORK

### 強制性規範
1. **檔案命名規範**
   - 前綴選擇決策樹（7種前綴）
   - 命名格式：前綴-主題核心概念
   - 長度控制：12-18字最佳

2. **YAML前置資料**
   - 6個必要欄位完整配置
   - 標準格式嚴格遵守

3. **四大類標籤系統**
   - 主題標籤（二級階層必須）
   - 類型標籤（必選1個）
   - 狀態標籤（必選1個）
   - 備註標籤（條件必須）

4. **內容結構**
   - 彙整者備註區（必須存在）
   - 導航系統（按長度觸發）
   - 章節樹格式（統一格式A）
   - 參考資料章節（條件必須）

### 視覺增強技術
- Callout區塊、摺疊區塊、引用區塊
- 表格組織、粗體斜體標記
- 列表應用原則、任務列表
- 表情符號圖標、水平分隔線

### 批量處理能力
- 相同主題筆記一致性
- 系列內容結構統一
- 版本更新自動遞增
- 標籤體系統一管理

## OUTPUT_SPECIFICATIONS
"將以下內容進行Obsidian格式標準化，嚴格遵守所有強制性規範：[PROMPT]"
````

### token_compression_framework
**Version Control:**
- Version: 2.0 (Token壓縮框架)
- Created: 2025-06-25
- Purpose: 高效壓縮prompt長度，保持功能完整性，提供三級壓縮模式
- Status: 最新開發，專門用於prompt優化

````
# token_compression_framework

## PURPOSE
高效壓縮prompt token數量，在保持功能完整性前提下最大化token節省效果。提供三級壓縮模式適應不同需求。

## CORE_FRAMEWORK

### 符號化標記系統
🔧 = 執行指令 | 📝 = 內容要求 | 🎯 = 輸出規範 | ⚠️ = 警告注意 | 🚫 = 禁止事項

### 條件邏輯壓縮
WHEN-DO格式: "當X情況時，執行Y" → "X ∈ Y"
EITHER-OR格式: "要麼A要麼B" → "A | B"
AND格式: "同時A和B" → "A & B"

### 三級壓縮模式
**Level 1: 基礎壓縮 (30-40%節省)**
- 適用條件: 初次使用、保守安全、AI模型未知
- 方法: 移除冗餘詞彙 + 基礎符號化
- 風險等級: 🟢 低風險

**Level 2: 標準壓縮 (50-60%節省)**
- 適用條件: 日常使用、熟悉AI模型、追求平衡效果
- 方法: 條件邏輯壓縮 + 結構重組
- 風險等級: 🟡 中風險

**Level 3: 極致壓縮 (70%+節省)**
- 適用條件: 專業用戶、token限制嚴格、追求極限效率
- 方法: 全套壓縮策略 + 創新簡化
- 風險等級: 🔴 高風險

### 回滾與恢復機制
自動回滾觸發條件:
- 功能保留度 < 60%: 自動降級到上一級壓縮
- 符號系統不支援: 自動轉換為純文字版本
- 邏輯關係破損: 恢復關鍵連接詞和結構

### 品質保證機制
發布前必檢清單:
□ 壓縮後prompt能獨立執行（零脈絡測試）
□ 核心功能保留度 ≥ 80%
□ 目標AI模型兼容性確認
□ 變數格式正確無誤
□ 專業術語保留適當
□ 邏輯關係清晰連貫
□ 符號系統有效性驗證
□ 回滾方案準備就緒

## OUTPUT_SPECIFICATIONS
基礎壓縮: "Compress prompt using Level 1 method: [PROMPT]"
標準壓縮: "Compress prompt using Level 2 method: [PROMPT]"
極致壓縮: "Compress prompt using Level 3 method: [PROMPT]"
````

---

## 對話串處理類型

### content_summarizing_organizer
**Version Control:**
- Version: 1.0 (對話串整理)
- Created: 2025-06
- Purpose: 專門用於AI對話串的整理和總結
- Status: 已被universal_information_organizer的SUMMARY_MODE整合

````
# content_summarizing_organizer

## PURPOSE
專門用於AI對話串的整理和總結，將複雜的對話內容轉化為結構化的知識回顧。

## CORE_FRAMEWORK

### 六步驟整理法
1. **主題概述**: 用2-3句話簡述討論的主要領域或主題範圍
2. **邏輯分類**: 按知識邏輯關係分成幾個主要類別，採用章節形式組織
3. **概念列舉**: 在每個類別下列出討論過的具體概念、理論或技術要點
4. **要點回顧**: 用簡潔但完整語言回顧每個要點核心內容
5. **關聯標註**: 特別標註概念之間的邏輯關係和相互影響
6. **學習路徑**: 如討論內容豐富，提供建議的學習路徑或深入方向

### 結構組織原則
- 保持清晰層次結構，使用適當標題分級
- 確保每個概念表述具有獨立性和完整性
- 重視概念間邏輯連結，避免孤立知識點

### 內容深度控制
- 平衡詳細程度與簡潔性
- 提供足夠回憶討論的訊息但避免冗長重述
- 識別核心概念和支撐細節，突出重要知識結構

## OUTPUT_SPECIFICATIONS
"使用六步驟整理法對以下對話內容進行結構化整理：[PROMPT]"
````

### memory_extraction_framework
**Version Control:**
- Version: 2.0 (記憶抽取框架)
- Created: 2025-06-25
- Purpose: 從完成專案中系統性抽取可重複使用的記憶、洞察和知識模式
- Status: 最新開發，專門用於專案經驗總結

````
# memory_extraction_framework

## PURPOSE
從已完成的專案、對話或學習經驗中，系統性地抽取具有長期價值的記憶、洞察和可重複應用的知識模式。

## CORE_FRAMEWORK

### 四階段執行流程

#### Phase 1: Experience Analysis Layer (5-10分鐘)
- 🔍 識別核心問題和解決方案
- 🔍 提取決策邏輯和權衡考量
- 🔍 記錄成功模式和失敗教訓
- 🔍 分析資源使用和效率優化

#### Phase 2: Knowledge Pattern Recognition (10-15分鐘)
- 🔍 跨案例的共同特徵識別
- 🔍 可重複應用的方法論抽取
- 🔍 領域特定vs通用原則區分
- 🔍 例外情況和邊界條件記錄

#### Phase 3: Memory Categorization System (5-10分鐘)
記憶類型分類（互斥分類）:
- **方法論記憶**: 工作流程、操作步驟、系統性方法
- **決策記憶**: 判斷標準、選擇邏輯、權衡考量
- **洞察記憶**: 深層理解、關鍵發現、突破性認知
- **經驗記憶**: 實戰教訓、成功模式、失敗預防
- **資源記憶**: 工具使用、資源配置、效率優化

#### Phase 4: Actionable Memory Generation (10-15分鐘)
- 🔍 轉換為具體可執行的指導原則
- 🔍 建立觸發條件和應用場景
- 🔍 設計檢驗標準和成功指標
- 🔍 預設例外處理和調整機制

### 記憶品質標準
高品質記憶特徵:
- **具體性**: 有明確的操作指導
- **可重複性**: 能在類似情境中重複應用
- **可驗證性**: 有明確的成功/失敗判斷標準
- **適應性**: 能根據情境變化進行調整
- **價值性**: 對未來決策和行動有實際幫助

### 記憶組織結構
```
記憶標題: [簡潔描述核心內容]
記憶類型: [方法論/決策/洞察/經驗/資源]
適用場景: [具體的應用情境描述]
核心內容: [記憶的具體內容]
觸發條件: [何時應該想起這個記憶]
應用方法: [如何具體應用這個記憶]
注意事項: [使用時需要注意的限制或風險]
相關記憶: [與其他記憶的關聯]
```

## OUTPUT_SPECIFICATIONS
"使用記憶抽取框架對以下完成的專案進行系統性記憶抽取，識別可重複應用的知識模式和洞察：[PROMPT]"
````

---

## 封存中-inactive

### system_prompt
**Version Control:**
- Version: 1.0
- Status: 封存中
- Purpose: 系統提示詞模板

````
# system_prompt

## PURPOSE
提供系統級提示詞的標準模板和設計指導。

## CORE_FRAMEWORK
[封存內容，不再維護]

## OUTPUT_SPECIFICATIONS
[已封存]
````
