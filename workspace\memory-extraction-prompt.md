# 專案記憶提煉Prompt

## PURPOSE
從專案記憶中提取**個案特例價值經驗**和**全域通用指導原則**，建立可重用的知識資產。

## 🎯 核心提取維度

### 1. 個案特例價值經驗
**具體技術決策與解決方案**：
- 特定問題的解決方法
- 技術選型的具體原因
- 實際遇到的坑和解決方案
- 環境特定的配置和約束

### 2. 全域通用指導原則
**可跨專案應用的智慧**：
- 設計原則和架構思維
- 工作流程和方法論
- 品質標準和最佳實踐
- 用戶偏好和約束模式

## 📝 記憶分析模式

### 發現的記憶規律 (基於8個Augment-Memories分析)

#### 個案特例模式
- **編碼問題**：PowerShell UTF-8 BOM vs no-BOM，CP950顯示
- **環境約束**：conda activate ai，特定GPU優先級
- **工具配置**：ComfyUI Desktop/Portable選擇，VSCode設定
- **架構決策**：模組化分層設計，單一職責原則

#### 通用指導模式
- **用戶偏好**：繁體中文溝通，copy-edit-check-replace工作流
- **品質標準**：全面測試含邊界情況，專業輸出風格
- **設計原則**：環境可重現性，零脈絡自解釋註解
- **錯誤處理**：重試機制，不直接放棄操作

## 📝 簡化輸出格式

### 個案特例卡片
```markdown
# [具體問題/情境]
**問題**：[遇到的具體問題]
**解決方案**：[採用的具體方法]
**關鍵細節**：[重要的技術細節或配置]
**適用條件**：[什麼情況下可以重用]
```

### 通用指導卡片
```markdown
# [原則名稱]
**核心理念**：[1-2句話說明]
**應用場景**：[什麼時候用]
**實踐方法**：[如何執行]
**注意事項**：[常見錯誤]
```

## 🎯 使用指令

### 分析專案記憶
"分析以下專案記憶，提取個案特例價值經驗和全域通用指導原則：[MEMORIES_CONTENT]"

### 分析Augment-Memories
"分析以下Augment-Memories內容，重點提取用戶偏好、技術約束、設計原則：[AUGMENT_MEMORIES_CONTENT]"

---
**Version**: 2.0 | **Created**: 2025-06-25 | **Updated**: 2025-06-25 | **Purpose**: 簡化的專案記憶提煉工具
