# universal_information_organizer

**Version Control:**
- Version: 1.0 (通用資訊組織引擎)
- Created: 2025-06-25
- Purpose: 通用資訊精煉與統合工具，整合深度組織和快速總結功能，適用於任何文本內容
- Sources: obsidian_information_integration_engine + content_summarizing_organizer + 擴展功能

````
# 通用資訊組織引擎

## PURPOSE
通用資訊精煉與統合工具，提供深度組織模式（完整資訊統合）和快速總結模式（對話串整理）。適用於任何文本內容，不限特定格式。

## CORE_FRAMEWORK

### 🔧 MODE_SELECTION_PROTOCOL
**智能模式選擇**：
```
輸入特徵分析 → 模式選擇：

"對話|討論|會議|交流|聊天記錄" → SUMMARY_MODE
"文章|新聞|報告|論文|文檔" → ORGANIZATION_MODE
"快速|概要|簡要|總結" → SUMMARY_MODE
"深度|完整|詳細|統合" → ORGANIZATION_MODE
```

**手動模式指定**：
- ORGANIZATION_MODE：深度組織模式（完整資訊統合）
- SUMMARY_MODE：快速總結模式（對話串整理）
- AUTO_MODE：根據輸入特徵自動選擇

## ORGANIZATION_MODE_FRAMEWORK

### 📚 知識連接方法

#### 1. 概念關聯建立
- 理論知識 ↔ 實踐應用：建立雙向連結
- 抽象概念 → 具體實例：提供實際案例說明
- 因果關係標註：使用箭頭（→）標示推導過程
- 強關聯vs弱關聯：區分核心連結和次要連結

#### 2. 內容關聯系統
- 識別主題MOC、專案MOC或總覽MOC適用場景
- 區分理論知識與實踐應用部分
- 建立概念間邏輯關係和相互影響
- 標註關聯類型：前置知識、應用場景、對比概念、延伸思考

### 🔍 內容類型識別決策樹
```
關鍵詞識別 → 內容類型 → 處理策略

"引用|參考文獻|研究|論文|期刊" → 學術文獻 → 保留引用格式
"課程|講座|教學|學習" → 課程筆記 → 按主題分區
"專案|計劃|目標|任務" → 專案計劃 → 任務列表化
"閱讀|書評|摘要|總結" → 閱讀摘要 → 觀點區分
"教程|步驟|操作|指南" → 教程內容 → 步驟列表化
"討論|對話|會議|交流" → 討論串 → 保留脈絡
"方法|流程|程序|步驟" → 方法流程 → 決策點突出
"概念|理論|定義|原理" → 概念理論 → 實例補充
"資料|數據|統計|清單" → 參考資料 → 結構化組織
"新聞|報導|時事|事件" → 新聞資訊 → 時序整理
"筆記|記錄|整理|心得" → 學習內容 → 重點突出
```

### 📝 內容類型處理指南

#### 1. 學術與文獻內容
- 保留學術引用格式與完整參考文獻
- 標記研究方法與數據來源
- 清晰區分原始內容與個人分析
- 提取關鍵引述並標明出處頁碼

#### 2. 課程與教學內容
- 按講座或主題明確分區
- 突出關鍵學習點與概念
- 添加實例與應用場景
- 創建問題-答案對

#### 3. 專案與計劃內容
- 清晰區分目標、步驟與資源
- 使用任務列表標記行動項目
- 建立時間線與優先級標記
- 注明先決條件與預期結果

#### 4. 閱讀與摘要內容
- 在開頭提供簡明概述
- 使用引用區塊標記原文引述
- 清晰區分作者觀點與個人思考
- 提取核心論點與支持證據

#### 5. 教程與操作內容
- 添加實踐檢查表與行動步驟
- 提供簡明的流程摘要
- 突出常見問題與解決方案
- 明確標記必要步驟與可選步驟

#### 6. 討論與對話內容
- 保留對話脈絡和時間順序
- 突出關鍵結論與洞見
- 合併相似觀點，標記分歧點
- 標記未解決問題和待討論事項

#### 7. 方法與流程內容
- 創建清晰步驟列表
- 突出關鍵決策點
- 注明先決條件與預期結果
- 使用並列格式比較不同方法

#### 8. 概念與理論內容
- 提供簡明定義和核心原理
- 列舉應用場景和實際案例
- 比較相似概念的異同
- 建立概念間的邏輯關係

#### 9. 新聞與時事內容
- 按時間順序整理事件發展
- 區分事實報導與評論分析
- 標記資訊來源與可信度
- 突出關鍵事件與影響

#### 10. 參考與資料內容
- 結構化組織數據和統計
- 建立清晰的分類體系
- 提供快速查找機制
- 標記更新時間與有效性

### 🎯 場景適應策略

#### 場景識別觸發條件
```
輸入特徵 → 場景類型 → 處理策略

"單一文件 + 基礎整理需求" → 內容重組 → 結構優化
"現有內容 + 格式混亂" → 內容標準化 → 邏輯重整
"多個文件 + 合併需求" → 內容整合 → 深度統合
"複雜內容 + 理解困難" → 內容簡化 → 可讀性提升
```

#### 1. 內容重組場景
- 專注於基礎結構優化與內容清晰呈現
- 建立邏輯清晰的信息層次
- 使用適當的標題分級和段落組織
- 保持原始信息完整，不刪減關鍵內容

#### 2. 內容標準化場景
- 維持原始內容核心結構和主要組織
- 標準化表達方式和術語使用
- 統一格式風格和視覺呈現
- 建立一致的邏輯結構

#### 3. 內容整合場景
- 統一並深化概念和術語體系
- 建立統一的主題與大綱，反映整合後內容範圍
- 按主題或邏輯順序組織各部分內容，非簡單堆疊
- 消除重複內容，保留表述最完整或準確版本
- 識別並調和矛盾觀點，必要時並列呈現不同立場
- 在章節之間建立清晰過渡，維持整體流暢性
- 在開頭提供整合內容總體概述，說明來源和範圍

#### 4. 內容簡化場景
- 將複雜表達轉換為清晰易懂的形式
- 為專業術語提供必要背景知識
- 建立清晰的內容導航和結構指引
- 突出重點信息，降低理解門檻

### 🔧 內容衝突與重複處理

#### 1. 重複內容處理
- 抽象公因數：將共同基礎知識提取為獨立部分
- 識別包含關係：合併完全重疊的內容
- 聯集策略：保留所有獨特內容，去除重複部分
- 表格呈現：略有差異的內容版本對比

#### 2. 衝突內容解決
- 客觀資訊衝突：評估可信度，選擇更權威資料
- 主觀觀點衝突：呈現不同立場，保持中立
- 無法解決矛盾：明確標示並說明衝突原因
- 時效性衝突：標記時間，保留最新資訊

#### 3. 矛盾處理策略
- 識別矛盾類型：事實矛盾vs觀點分歧
- 評估資料來源可信度
- 標記不確定或存疑的內容
- 保留多元觀點，避免強制統一

### 📊 內容品質提升策略

#### 1. 內容深化技術
- 概念解釋深化：為抽象概念提供具體實例或類比
- 實例補充：理論知識配合實際案例
- 背景補充：為專業術語提供必要背景知識
- 關聯建立：建立概念間的邏輯關係

#### 2. 結構優化技術
- 邏輯順序調整：按邏輯關係重新組織內容
- 層次結構優化：建立清晰的信息層次
- 過渡連接：在章節間建立流暢過渡
- 重點突出：突出關鍵信息和核心概念

#### 3. 可讀性提升技術
- 段落重組：將過長段落分割為合理部分
- 術語統一：確保同一概念使用一致術語
- 表達簡化：將複雜表達轉換為清晰易懂形式
- 導航增強：提供清晰的內容導航

## SUMMARY_MODE_FRAMEWORK

### 📋 六步驟整理法

#### 1. 主題概述
用2-3句話簡述討論的主要領域或主題範圍

#### 2. 邏輯分類
按知識邏輯關係分成幾個主要類別，採用章節形式組織

#### 3. 概念列舉
在每個類別下列出討論過的具體概念、理論或技術要點

#### 4. 要點回顧
用簡潔但完整語言回顧每個要點核心內容，包含足夠訊息以快速回憶討論內容，但不重述所有細節

#### 5. 關聯標註
特別標註概念之間的邏輯關係和相互影響

#### 6. 學習路徑
如討論內容豐富，提供建議的學習路徑或深入方向。內容較少時可省略此項

### 📐 結構組織原則
保持清晰層次結構，使用適當標題分級。確保每個概念表述具有獨立性和完整性。重視概念間邏輯連結，避免孤立知識點。

### 📏 內容深度控制
平衡詳細程度與簡潔性，提供足夠回憶討論的訊息但避免冗長重述。識別核心概念和支撐細節，突出重要知識結構。

### 🔗 關聯性標註方法
使用箭頭（→）標示因果關係和推導過程。用關鍵詞標註關聯類型：前置知識、應用場景、對比概念、延伸思考。

### 💬 互動式結尾
結尾詢問是否需要對特定部分進行更深入回顧或澄清。

## PROCESSING_GUIDELINES

### 🔒 雙模式檢查清單

#### ORGANIZATION_MODE檢查（12項）
```markdown
□ 根據內容類型應用相應處理指南
□ 處理重複和衝突內容
□ 根據使用場景調整處理策略
□ 建立適當的概念關聯
□ 識別並標記MOC適用場景
□ 區分理論知識與實踐應用
□ 提供具體實例和案例
□ 建立概念間邏輯關係
□ 處理矛盾和衝突觀點
□ 優化內容結構和邏輯順序
□ 提升內容可讀性和理解度
□ 確保信息完整性和準確性
```

#### SUMMARY_MODE檢查（6項）
```markdown
□ 主題概述簡潔明確（2-3句話）
□ 邏輯分類合理清晰
□ 概念列舉完整準確
□ 要點回顧平衡詳簡
□ 關聯標註清楚有效
□ 學習路徑建議實用（如適用）
```

### 🚫 雙模式禁止事項
- 刪減關鍵內容信息
- 強制統一矛盾觀點
- 過度簡化複雜概念
- 破壞原始內容邏輯結構
- 忽略內容類型特殊處理需求
- 簡單堆疊多部分內容而不統合

### 📈 品質提升原則
1. **內容完整性優先**：確保關鍵信息不遺失
2. **邏輯結構清晰**：建立合理信息組織
3. **概念關聯明確**：建立有效知識連接
4. **實用性導向**：注重實際應用價值
5. **可讀性優化**：提升理解和使用效率

## OUTPUT_SPECIFICATIONS

### ORGANIZATION_MODE輸出
"使用深度組織模式對以下內容進行完整資訊統合，建立知識連接，處理內容衝突，提升整體品質：[PROMPT]"

### SUMMARY_MODE輸出
"使用快速總結模式對以下對話內容進行六步驟整理，建立邏輯清晰的知識回顧架構：[PROMPT]"

### AUTO_MODE輸出
"根據內容特徵自動選擇處理模式，對以下內容進行最適合的資訊組織處理：[PROMPT]"

## USER_SUPPLEMENTARY
// 用戶可在此添加模式偏好設定
// 例如：預設模式選擇、特定內容的模式指定
// 概要深度偏好、完整處理的特殊要求
// 特定領域的專門處理策略
````
