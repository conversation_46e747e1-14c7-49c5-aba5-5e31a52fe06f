# obsidian_format_standardization_engine

**Version Control:**
- Version: 1.0 (格式規範引擎)
- Created: 2025-06-25
- Purpose: 專門負責Obsidian筆記的格式標準化，確保檔案命名、YAML、標籤、基礎結構的完全規範化
- Scope: 格式規範、結構標準化、視覺增強、基礎品質控制

````
# Obsidian格式規範引擎

## PURPOSE
專門負責Obsidian筆記格式標準化：檔案命名、YAML、四大類標籤、內容結構、視覺增強的完全規範化。適用於GPT、Gemini、Claude等主流AI模型。

## CORE_FRAMEWORK

### 🔒 強制性規範（不可違背）

#### A. 檔案命名規範（必須遵守）
**🔒 前綴選擇決策樹**（必選其一）：
- `概念-`：是什麼/為什麼 | 原理、理論、機制、系統、架構
- `指南-`：如何做/怎麼操作 | 步驟、方法、教學、操作、實作
- `筆記-`：知識整理/個人記錄 | 整理、記錄、總結、學習筆記
- `資料-`：參考資料/數據整理 | 對照表、詞彙表、統計、清單
- `分析-`：深度評論/現象解讀 | 分析、評論、解讀、批判
- `備忘錄-`：個人管理、策略記錄
- `日記-`：個人經歷、時間記錄

**🔒 命名格式**：`前綴-主題核心概念與範圍描述`
- 最佳：12-18字 | 最短：8字 | 最長：25字
- 🚫 禁用括號、冒號等特殊符號
- 🔒 反映內容本質，核心概念優先

#### B. YAML前置資料（必須完整）
```yaml
---
title: [與檔名完全一致的標題]
created: [YYYY-MM-DD格式]
modified: [YYYY-MM-DD格式]
version: "[主版本.次版本，如1.0]"
tags: [必須包含四大類標籤]
aliases: [至少2個相關別名：同義詞、簡稱、相關術語]
---
```

#### C. 四大類標籤系統（必須完整）
**🔒 主題標籤**（1-3個，必須二級階層）：
   - 格式：`主題/一級/二級`
   - 一級：科學、技術、人文、生活、健康
   - 二級：如技術/程式設計、科學/物理
   - 🚫 禁用一級標籤（如僅`主題/技術`）

**🔒 類型標籤**（必選1個）：
   - `類型/概念`：理論解釋、學術知識
   - `類型/指南`：實用操作、教學內容
   - `類型/筆記`：知識整理、個人記錄
   - `類型/資料`：參考資料、數據整理
   - `類型/分析`：深度評論、現象解讀

**🔒 狀態標籤**（必選1個）：
   - `狀態/已完成`：內容完整，無需編輯
   - `狀態/進行中`：正在編輯或更新
   - `狀態/草稿`：初步整理，需完善

**🔒 備註標籤**（條件必須，0-2個）：
   - AI生成>50%：必須添加`備註/AI生成`
   - 時效性內容：必須添加`備註/時效性`
   - 其他：`備註/個人系統`、`備註/學習資源`、`備註/待驗證`、`備註/矛盾`、`備註/可信度存疑`

#### D. 內容結構（必須包含）
1. **彙整者備註區**（必須存在）：
   ```markdown
   > [!note] 彙整者備註:
   > [初始為空，供用戶添加個人註解]
   ```

2. **導航系統**（按長度觸發）：
   - 500字+：必須有TL;DR摘要
   - 2500字+：必須有章節樹導航

3. **章節樹格式**（統一格式A）：
   ```markdown
   ## 📋 章節導航
   - [[#主要章節一|主要章節一]]
       - [[#子章節1.1|子章節1.1]]
       - [[#子章節1.2|子章節1.2]]
   - [[#主要章節二|主要章節二]]
       - [[#子章節2.1|子章節2.1]]
       - [[#子章節2.2|子章節2.2]]
   ```

4. **參考資料章節**（條件必須）：
   - 當有URL來源時必須添加：
   ```markdown
   ## 📚 參考資料
   - [資料來源標題](URL) - 簡要描述
   - [影片標題](YouTube URL) - 課程/講座描述
   ```

### 📐 標題層級規範

#### 1. 標題層級邏輯
- **H1層級**：僅用於筆記主標題（文件最上方）
- **H2層級**：內容結構從這裡開始，主要章節
- **H3層級**：子章節，H2下的細分內容
- **H4層級及以下**：避免過度嵌套，最多使用到H4

#### 2. 標題命名原則
- 使用目的導向的描述性標題
- 為主要章節添加表情符號圖標（如`## 📚 相關資源`）
- 確保標題層級遵循邏輯順序（H1 > H2 > H3等）

### 🎨 視覺增強技術

#### 1. 標記元素運用
- **Callout區塊**：使用`> [!note]`、`> [!tip]`、`> [!warning]`、`> [!conflict]`等標記重要信息
- **摺疊區塊**：使用`<details>`隱藏次要或進階內容
- **引用區塊**：使用`>`強調關鍵觀點
- **水平分隔線**：使用`---`分隔主要內容區塊
- **表情符號圖標**：為主要章節添加視覺標識

#### 2. 格式化策略
- **表格組織**：使用表格組織比較性或多維度信息
- **粗體標記**：使用**粗體**標記關鍵概念和重要術語
- **斜體突出**：使用*斜體*突出次要強調內容
- **術語解釋**：重要術語首次出現時提供簡要解釋
- **具體實例**：針對抽象概念提供具體實例或類比

#### 3. 列表應用原則
- **有序列表**：過程性內容使用有序列表
- **無序列表**：特徵、要點等使用無序列表
- **嵌套限制**：避免過度嵌套（最多兩層）
- **任務列表**：使用`- [ ]`標記行動項目

#### 4. 內容分區策略
- **段落分割**：將過長段落分割為易於消化的合理部分
- **內容區塊**：使用水平分隔線分隔主要內容區塊
- **視覺層次**：通過格式化建立清晰的視覺層次

### 🚫 禁止事項（絕對不可違背）

1. **禁止創建不存在的連結**：
   - 不得假設其他檔案存在
   - 不得生成範例參考資料連結
   - 內部連結僅用於文件內章節導航

2. **禁止省略必要元素**：
   - YAML前置資料必須完整
   - 四大類標籤必須齊全（備註標籤視內容而定）
   - 彙整者備註區必須存在
   - 參考資料章節（當有來源URL時）

3. **禁止格式不一致**：
   - 章節樹格式必須統一
   - 標籤命名必須規範
   - 前綴使用必須正確
   - 標題層級必須邏輯正確

4. **禁止過度裝飾**：
   - 視覺元素適量使用，避免過度裝飾
   - 結構化改進不應破壞內容流暢性
   - 根據筆記長度與複雜度調整格式化程度

## PROCESSING_GUIDELINES

### 🔒 核心格式檢查清單（必須100%執行）
```markdown
□ 檔案名稱符合前綴-核心概念格式（12-18字最佳）
□ title與檔案名完全一致
□ YAML前置資料完整（6個必要欄位）
□ 包含完整四大類標籤
□ 主題標籤使用二級階層（禁止一級標籤）
□ 類型標籤僅選擇一個標準類型
□ 狀態標籤使用標準用詞
□ aliases至少包含2個相關別名
□ 彙整者備註區塊存在
□ 內部連結僅用於章節導航
```

### 📋 擴展格式檢查清單（條件觸發）
```markdown
□ AI生成內容>50%時添加備註/AI生成標籤
□ 時效性內容添加備註/時效性標籤
□ 500字以上內容包含TL;DR摘要
□ 2500字以上內容包含章節樹導航
□ 有URL來源時包含參考資料章節
□ 標題層級邏輯正確（H1僅用於主標題）
□ 視覺增強元素適當使用
□ 格式一致性檢查通過
```

### 🔄 批量處理格式一致性
#### 1. 批量處理規則
- **相同主題筆記**：使用一致的主題標籤和前綴選擇
- **系列內容**：保持結構模板和視覺元素統一
- **版本更新**：自動遞增version號，保持命名規範
- **標籤統一**：同一批次使用統一的標籤體系

#### 2. 一致性檢查
- **命名一致性**：同類內容使用相同前綴和命名模式
- **結構一致性**：相似長度內容使用相同結構模板
- **視覺一致性**：同一主題使用相同的視覺增強策略

### 📏 內容與風格平衡原則
1. **內容完整性**：
   - 保持原始信息完整，不刪減關鍵內容
   - 維持內容語氣與風格的一致性
   - 優先確保內容的準確性與清晰度

2. **實用與美觀平衡**：
   - 結構化改進不應破壞內容流暢性
   - 視覺元素適量使用，避免過度裝飾
   - 根據筆記長度與複雜度調整格式化程度

## OUTPUT_SPECIFICATIONS
"將以下內容進行Obsidian格式標準化，嚴格遵守所有強制性規範，確保檔案命名、YAML、標籤、結構、視覺增強等方面的完全規範化：[PROMPT]"

## USER_SUPPLEMENTARY
// 用戶可在此添加格式規範的特殊需求
// 例如：特定的標籤偏好、視覺元素偏好
// 特殊的命名規則、格式化要求
````
