# obsidian_note_optimizer_3.0_ULTIMATE

**Version Control:**
- Version: 3.0 ULTIMATE (基於meta-prompt優化經驗的終極版)
- Created: 2025-06-25
- Token Target: 1200-1800 (最佳效率範圍)
- Purpose: 基於60個文件規格化實戰經驗，解決命名域混淆問題的Obsidian筆記優化器
- Innovation: 三層分離架構 + 問題導向設計 + 實戰驗證簡化

````
# obsidian_note_optimizer_3.0_ULTIMATE

## PURPOSE
基於60個文件規格化實戰經驗，將任何內容轉換為符合個人標準的Obsidian筆記。解決AI理解混淆問題，確保100%規範遵守。

## META_EXECUTION_LAYER

### 🔧 CORE_PROBLEMS_SOLVED
**META_INSTRUCTION**: 你(AI)必須解決以下核心問題
- 🔧 檔案命名混亂 → 前綴決策樹自動選擇
- 🔧 標籤系統不一致 → 四大類強制檢查
- 🔧 結構缺失 → 長度觸發導航系統
- 🔧 格式不統一 → 標準模板強制應用

### 🔧 EXECUTION_PRIORITY_ENGINE
**META_PROCESS**: 你(AI)按優先級執行
```
🔧 P1_CRITICAL (必須100%執行):
檔案命名 → YAML完整 → 四大類標籤 → 彙整者備註

🔧 P2_IMPORTANT (長度觸發):
500字+ → TL;DR摘要
2500字+ → 章節樹導航

🔧 P3_CONDITIONAL (條件觸發):
有URL → 參考資料章節
AI生成>50% → 備註/AI生成標籤
時效性內容 → 備註/時效性標籤
```

### 🔧 FILENAME_DECISION_ENGINE
**META_INSTRUCTION**: 你(AI)根據內容關鍵詞自動選擇前綴
```
🔧 關鍵詞匹配決策:
"原理|理論|機制|系統|架構|是什麼|為什麼" → 概念-
"步驟|方法|教學|操作|實作|如何做|怎麼操作" → 指南-
"整理|記錄|總結|學習筆記" → 筆記-
"對照表|詞彙表|統計|清單|參考" → 資料-
"分析|評論|解讀|批判|現象" → 分析-
"個人管理|策略記錄" → 備忘錄-
"個人經歷|時間記錄" → 日記-
```

## SUB_GENERATION_LAYER

### 📝 YAML_TEMPLATE_STRUCTURE
**SUB_REQUIREMENT**: 生成的筆記必須包含此YAML結構
```yaml
---
title: [與檔名完全一致]
created: [YYYY-MM-DD]
modified: [YYYY-MM-DD]
version: "[X.Y格式]"
tags: [四大類標籤完整]
aliases: [至少2個別名]
---
```

### 📝 FOUR_TAG_SYSTEM_PROTOCOL
**SUB_CONTENT**: 標籤系統必須遵循此規範
```
📝 主題標籤 (1-3個): 主題/一級/二級 (禁止一級標籤)
📝 類型標籤 (必須1個): 類型/概念|指南|筆記|資料|分析
📝 狀態標籤 (必須1個): 狀態/已完成|進行中|草稿
📝 備註標籤 (0-2個): 備註/AI生成|時效性|個人系統|學習資源|待驗證
```

### 📝 CONTENT_STRUCTURE_TEMPLATE
**SUB_REQUIREMENT**: 內容結構必須包含
```
📝 彙整者備註區 (必須):
> [!note] 彙整者備註:
> [初始為空，供用戶添加個人註解]

📝 TL;DR摘要 (500字+觸發):
## 🎯 TL;DR
[3-5句話核心摘要]

📝 章節樹導航 (2500字+觸發):
## 📋 章節導航
- [[#章節名|章節名]]
    - [[#子章節|子章節]]

📝 參考資料 (有URL時觸發):
## 📚 參考資料
- [標題](URL) - 描述
```

## OUTPUT_SPECIFICATION_LAYER

### 🎯 QUALITY_ASSURANCE_CHECKLIST
**OUTPUT_REQUIREMENT**: 最終筆記必須通過此檢查
```
🎯 P1_CRITICAL_CHECK (必須100%):
□ 檔案名12-18字，前綴正確
□ title與檔名完全一致
□ 四大類標籤完整無缺
□ 彙整者備註區存在

🎯 P2_LENGTH_TRIGGERED_CHECK:
□ 500字+ → TL;DR摘要存在
□ 2500字+ → 章節樹導航存在

🎯 P3_CONDITIONAL_CHECK:
□ AI生成>50% → 備註/AI生成標籤
□ 時效性內容 → 備註/時效性標籤
□ 有URL → 參考資料章節
```

### 🎯 FORBIDDEN_ACTIONS_PROTOCOL
**OUTPUT_RESTRICTION**: 絕對禁止以下行為
```
🚫 檔案命名: 括號、冒號、特殊符號
🚫 標籤創新: 不得創造新的類型標籤
🚫 連結假設: 不得創建不存在的檔案連結
🚫 元素省略: YAML、標籤、彙整者備註不得省略
🚫 格式混亂: 章節樹格式必須統一
```

## PROCESSING_GUIDELINES

### 🔧 EXECUTION_WORKFLOW
**三步驟執行法** (基於meta-prompt經驗):
1. **內容分析** (30秒): 識別關鍵詞 → 選擇前綴 → 判斷長度觸發
2. **結構生成** (60秒): 應用YAML模板 → 四大類標籤 → 內容結構
3. **品質檢查** (30秒): P1必檢 → P2長度檢查 → P3條件檢查

### 🔧 COMMON_FAILURE_PREVENTION
**基於60個文件經驗的錯誤預防**:
```
🔧 失敗模式1: 前綴選擇錯誤 (發生率35%)
症狀: 概念內容用指南前綴，操作內容用概念前綴
預防: 嚴格按關鍵詞匹配決策樹，優先考慮內容主要目的

🔧 失敗模式2: 標籤不完整 (發生率28%)
症狀: 缺少狀態標籤或使用一級主題標籤
預防: 四大類標籤強制檢查清單，AI必須逐項確認

🔧 失敗模式3: 結構缺失 (發生率22%)
症狀: 長文缺少導航，短文強加TL;DR
預防: 長度觸發的自動判斷機制，500字和2500字明確閾值

🔧 失敗模式4: 格式不一致 (發生率15%)
症狀: 章節樹格式混亂，YAML格式錯誤
預防: 標準模板強制應用，禁止任何格式創新
```

### 🔧 REAL_WORLD_OPTIMIZATION
**基於實戰經驗的優化策略**:
```
🔧 優化1: 智能前綴選擇
當關鍵詞衝突時 → 按內容主要目的判斷
範例: "分析方法" → 若重點在方法則用"指南-"，若重點在分析結果則用"分析-"

🔧 優化2: 動態標籤適配
技術內容 → 主題/技術/程式設計|AI|數據科學
學術內容 → 主題/科學/物理|化學|數學
生活內容 → 主題/生活/健康|理財|效率

🔧 優化3: 內容長度智能判斷
實際字數統計 → 排除YAML和標記符號
純內容500字+ → 觸發TL;DR
純內容2500字+ → 觸發章節樹

🔧 優化4: 品質保證機制
生成後自動檢查 → 發現問題立即修正
檢查順序: 檔名 → YAML → 標籤 → 結構 → 格式
```

## OUTPUT_SPECIFICATIONS

### STANDARD_MODE_OUTPUT
"將以下內容轉換為標準化Obsidian筆記，嚴格遵守三層架構和品質檢查清單: [PROMPT]"

### BATCH_MODE_OUTPUT
"批量處理以下內容為標準化Obsidian筆記，確保格式一致性: [PROMPT]"

## INFORMATION_INTEGRATION_LAYER

### 📝 KNOWLEDGE_CONNECTION_PROTOCOL
**SUB_REQUIREMENT**: 筆記必須具備知識連接能力
```
📝 內部連結策略:
- 文件內章節導航 → 使用[[#章節名|章節名]]格式
- 已存在文件連結 → 僅連結確實存在的文件
- 🚫 禁止假設性連結 → 不創建不存在的文件連結
- 🚫 禁止"相關筆記"章節 → 避免混淆已有與假設條目

📝 概念關聯系統:
- 理論知識 ↔ 實踐應用 → 建立雙向連結
- 抽象概念 → 具體實例 → 提供實際案例說明
- 主題MOC識別 → 標記總覽型筆記的適用場景
- 強關聯vs弱關聯 → 區分核心連結和次要連結
```

### 📝 CONTENT_TYPE_PROCESSING_ENGINE
**SUB_INSTRUCTION**: 你(AI)根據內容類型應用專門處理策略
```
📝 學術文獻處理:
- 保留完整引用格式和參考文獻
- 標記研究方法與數據來源
- 區分原始內容與個人分析
- 提取關鍵引述並標明出處頁碼

📝 討論串記錄處理:
- 保留對話脈絡和時間順序
- 突出關鍵結論與洞見
- 合併相似觀點，標記分歧點
- 標記未解決問題和待討論事項

📝 教程內容處理:
- 創建清晰的步驟列表
- 突出關鍵決策點和注意事項
- 添加實踐檢查表與行動步驟
- 明確標記必要步驟與可選步驟

📝 概念理論處理:
- 提供簡明定義和核心原理
- 列舉應用場景和實際案例
- 比較相似概念的異同
- 建立概念間的邏輯關係
```

### 📝 SCENARIO_ADAPTATION_PROTOCOL
**SUB_PROCESS**: 你(AI)根據使用場景調整處理策略
```
📝 資料轉筆記場景:
- 專注基本結構優化與內容清晰呈現
- 輕量使用知識連接，主要關注關鍵術語
- 使用簡單扼要的別名和基礎標籤

📝 筆記重整標準化場景:
- 保留原始創建日期，更新修改日期
- 維持原始核心結構和主要內容組織
- 標準化標題層級和格式確保一致性
- 重新組織標籤系統為四大功能分類

📝 多筆記整合場景:
- 統一並深化別名和標籤體系
- 按主題或邏輯順序組織，非簡單堆疊
- 消除重複內容，保留最完整準確版本
- 識別並調和矛盾觀點，必要時並列呈現
```

### 📝 CONFLICT_RESOLUTION_ENGINE
**SUB_REQUIREMENT**: 處理內容衝突與重複的標準方法
```
📝 重複內容處理:
- 抽象公因數 → 提取共同基礎知識為獨立部分
- 識別包含關係 → 合併完全重疊的內容
- 聯集策略 → 保留所有獨特內容，去除重複
- 表格呈現 → 略有差異的內容版本對比

📝 衝突內容解決:
- 客觀資訊衝突 → 評估可信度，選擇權威資料
- 主觀觀點衝突 → 呈現不同立場，保持中立
- 無法解決矛盾 → 使用> [!conflict]標示
- 時效性衝突 → 標記時間，保留最新資訊
```

### 🎯 BATCH_PROCESSING_PROTOCOL
**META_INSTRUCTION**: 你(AI)處理多個文件時的一致性保證
```
🔧 批量處理規則:
- 相同主題 → 使用一致的主題標籤
- 系列內容 → 保持前綴和結構統一
- 版本更新 → 自動遞增version號
- 交叉引用 → 僅在確實存在時建立連結
```

### 🎯 CONTENT_TYPE_ADAPTATION
**META_PROCESS**: 你(AI)根據內容類型自動調整
```
🔧 學術論文適配:
前綴選擇 → 概念-/分析- 優先
標籤重點 → 主題/科學/具體領域
必要結構 → 參考資料章節強制要求

🔧 技術文檔適配:
前綴選擇 → 指南-/筆記- 優先
標籤重點 → 主題/技術/具體技術
必要結構 → 代碼塊保持格式

🔧 個人筆記適配:
前綴選擇 → 筆記-/備忘錄- 優先
標籤重點 → 狀態/進行中 常用
必要結構 → 彙整者備註重點強化
```

### 🎯 QUALITY_EVOLUTION_TRACKING
**META_INSTRUCTION**: 你(AI)持續改進筆記品質
```
🔧 品質指標追蹤:
- 檔名準確度: 是否精確反映內容核心
- 標籤完整度: 四大類標籤是否齊全準確
- 結構適配度: 導航系統是否符合內容長度
- 格式一致性: 是否嚴格遵守標準模板

🔧 改進建議生成:
檔名過長 → 建議精簡核心概念
標籤過泛 → 建議使用更具體的二級分類
結構過簡 → 建議增加章節樹導航
格式偏差 → 建議回歸標準模板
```

## CORE_INSIGHTS_FROM_METAPROMPT_EXPERIENCE

### 💡 關鍵洞察應用
**基於meta-prompt優化專案的核心發現**:
```
💡 洞察1: 問題導向勝過功能導向
應用: 聚焦解決檔名混亂、標籤不一致、結構缺失三大核心問題

💡 洞察2: 簡潔性是複雜性的最高形式
應用: 用三層分離架構替代複雜的規範描述

💡 洞察3: 實戰驗證勝過理論推導
應用: 基於60個文件的實際錯誤模式設計預防機制

💡 洞察4: AI友好設計勝過人類邏輯習慣
應用: 明確區分META指令、SUB要求、OUTPUT規範
```

### 💡 Token效率優化
**基於1200-1800 token目標的優化**:
```
💡 效率策略1: 功能密度最大化
移除解釋性描述 → 直接執行指令
範例: "必須遵守" → "🔧 META_INSTRUCTION"

💡 效率策略2: 符號化標記系統
🔧 = META層指令 (AI當下執行)
📝 = SUB層要求 (筆記內容)
🎯 = OUTPUT層規範 (最終產出)

💡 效率策略3: 條件邏輯壓縮
"如果內容超過500字，則添加TL;DR" → "500字+ → TL;DR摘要"

💡 效率策略4: 檢查清單標準化
複雜描述 → 簡潔檢查項目
"檔案名稱必須符合前綴-核心概念格式且長度在12-18字之間" → "□ 檔案名12-18字，前綴正確"
```

## USER_SUPPLEMENTARY
// 用戶個人偏好設定區域
// 特定主題的標籤偏好
// 個人化的前綴選擇規則
// 特殊格式要求
// 基於使用經驗的個性化調整
````
