# Obsidian Optimization Toolkit

**Version**: 1.0 Final  
**Created**: 2025-06-25  
**Purpose**: Obsidian筆記優化工具包，包含通用資訊組織引擎和格式規範引擎

---

## 🔧 Tool 1: Universal Information Organizer

**用途**: 通用資訊精煉與統合，適用任何文本內容，提供深度組織和快速總結兩種模式

```
# universal_information_organizer

## PURPOSE
通用資訊精煉與統合工具，提供深度組織模式（完整資訊統合）和快速總結模式（對話串整理）。適用於任何文本內容，不限特定格式。

## MODE_SELECTION
輸入特徵分析 → 模式選擇：
"對話|討論|會議|交流|聊天記錄" → SUMMARY_MODE
"文章|新聞|報告|論文|文檔" → ORGANIZATION_MODE
"快速|概要|簡要|總結" → SUMMARY_MODE
"深度|完整|詳細|統合" → ORGANIZATION_MODE

手動模式指定：
- ORGANIZATION_MODE：深度組織模式（完整資訊統合）
- SUMMARY_MODE：快速總結模式（對話串整理）
- AUTO_MODE：根據輸入特徵自動選擇

## ORGANIZATION_MODE_FRAMEWORK

### 知識連接方法
- 理論知識 ↔ 實踐應用：建立雙向連結
- 抽象概念 → 具體實例：提供實際案例說明
- 因果關係標註：使用箭頭（→）標示推導過程
- 強關聯vs弱關聯：區分核心連結和次要連結

### 內容類型識別決策樹
"引用|參考文獻|研究|論文|期刊" → 學術文獻 → 保留引用格式
"課程|講座|教學|學習" → 課程筆記 → 按主題分區
"專案|計劃|目標|任務" → 專案計劃 → 任務列表化
"閱讀|書評|摘要|總結" → 閱讀摘要 → 觀點區分
"教程|步驟|操作|指南" → 教程內容 → 步驟列表化
"討論|對話|會議|交流" → 討論串 → 保留脈絡
"方法|流程|程序|步驟" → 方法流程 → 決策點突出
"概念|理論|定義|原理" → 概念理論 → 實例補充
"資料|數據|統計|清單" → 參考資料 → 結構化組織
"新聞|報導|時事|事件" → 新聞資訊 → 時序整理
"筆記|記錄|整理|心得" → 學習內容 → 重點突出

### 場景適應策略
"單一文件 + 基礎整理需求" → 內容重組 → 結構優化
"現有內容 + 格式混亂" → 內容標準化 → 邏輯重整
"多個文件 + 合併需求" → 內容整合 → 深度統合
"複雜內容 + 理解困難" → 內容簡化 → 可讀性提升

### 內容衝突與重複處理
- 抽象公因數：將共同基礎知識提取為獨立部分
- 識別包含關係：合併完全重疊的內容
- 聯集策略：保留所有獨特內容，去除重複部分
- 表格呈現：略有差異的內容版本對比
- 客觀資訊衝突：評估可信度，選擇更權威資料
- 主觀觀點衝突：呈現不同立場，保持中立
- 無法解決矛盾：明確標示並說明衝突原因

### 內容品質提升策略
- 概念解釋深化：為抽象概念提供具體實例或類比
- 實例補充：理論知識配合實際案例
- 背景補充：為專業術語提供必要背景知識
- 關聯建立：建立概念間的邏輯關係
- 邏輯順序調整：按邏輯關係重新組織內容
- 層次結構優化：建立清晰的信息層次
- 過渡連接：在章節間建立流暢過渡
- 重點突出：突出關鍵信息和核心概念

## SUMMARY_MODE_FRAMEWORK

### 六步驟整理法
1. 主題概述：用2-3句話簡述討論的主要領域或主題範圍
2. 邏輯分類：按知識邏輯關係分成幾個主要類別，採用章節形式組織
3. 概念列舉：在每個類別下列出討論過的具體概念、理論或技術要點
4. 要點回顧：用簡潔但完整語言回顧每個要點核心內容，包含足夠訊息以快速回憶討論內容，但不重述所有細節
5. 關聯標註：特別標註概念之間的邏輯關係和相互影響
6. 學習路徑：如討論內容豐富，提供建議的學習路徑或深入方向。內容較少時可省略此項

### 結構組織原則
保持清晰層次結構，使用適當標題分級。確保每個概念表述具有獨立性和完整性。重視概念間邏輯連結，避免孤立知識點。

### 內容深度控制
平衡詳細程度與簡潔性，提供足夠回憶討論的訊息但避免冗長重述。識別核心概念和支撐細節，突出重要知識結構。

### 關聯性標註方法
使用箭頭（→）標示因果關係和推導過程。用關鍵詞標註關聯類型：前置知識、應用場景、對比概念、延伸思考。

### 互動式結尾
結尾詢問是否需要對特定部分進行更深入回顧或澄清。

## PROCESSING_GUIDELINES

### 雙模式檢查清單
ORGANIZATION_MODE檢查（12項）:
□ 根據內容類型應用相應處理指南
□ 處理重複和衝突內容
□ 根據使用場景調整處理策略
□ 建立適當的概念關聯
□ 識別並標記MOC適用場景
□ 區分理論知識與實踐應用
□ 提供具體實例和案例
□ 建立概念間邏輯關係
□ 處理矛盾和衝突觀點
□ 優化內容結構和邏輯順序
□ 提升內容可讀性和理解度
□ 確保信息完整性和準確性

SUMMARY_MODE檢查（6項）:
□ 主題概述簡潔明確（2-3句話）
□ 邏輯分類合理清晰
□ 概念列舉完整準確
□ 要點回顧平衡詳簡
□ 關聯標註清楚有效
□ 學習路徑建議實用（如適用）

### 禁止事項
- 刪減關鍵內容信息
- 強制統一矛盾觀點
- 過度簡化複雜概念
- 破壞原始內容邏輯結構
- 忽略內容類型特殊處理需求
- 簡單堆疊多部分內容而不統合

### 品質提升原則
1. 內容完整性優先：確保關鍵信息不遺失
2. 邏輯結構清晰：建立合理信息組織
3. 概念關聯明確：建立有效知識連接
4. 實用性導向：注重實際應用價值
5. 可讀性優化：提升理解和使用效率

## OUTPUT_SPECIFICATIONS
ORGANIZATION_MODE輸出：
"使用深度組織模式對以下內容進行完整資訊統合，建立知識連接，處理內容衝突，提升整體品質：[PROMPT]"

SUMMARY_MODE輸出：
"使用快速總結模式對以下對話內容進行六步驟整理，建立邏輯清晰的知識回顧架構：[PROMPT]"

AUTO_MODE輸出：
"根據內容特徵自動選擇處理模式，對以下內容進行最適合的資訊組織處理：[PROMPT]"
```

---

## 🎨 Tool 2: Obsidian Format Standardization Engine

**用途**: 專門負責Obsidian筆記格式標準化，包含檔案命名、YAML、標籤、結構、視覺增強

```
# obsidian_format_standardization_engine

## PURPOSE
專門負責Obsidian筆記格式標準化：檔案命名、YAML、四大類標籤、內容結構、視覺增強的完全規範化。適用於GPT、Gemini、Claude等主流AI模型。

## CORE_FRAMEWORK

### 強制性規範（不可違背）

#### A. 檔案命名規範（必須遵守）
前綴選擇決策樹（必選其一）：
- `概念-`：是什麼/為什麼 | 原理、理論、機制、系統、架構
- `指南-`：如何做/怎麼操作 | 步驟、方法、教學、操作、實作
- `筆記-`：知識整理/個人記錄 | 整理、記錄、總結、學習筆記
- `資料-`：參考資料/數據整理 | 對照表、詞彙表、統計、清單
- `分析-`：深度評論/現象解讀 | 分析、評論、解讀、批判
- `備忘錄-`：個人管理、策略記錄
- `日記-`：個人經歷、時間記錄

命名格式：`前綴-主題核心概念與範圍描述`
- 最佳：12-18字 | 最短：8字 | 最長：25字
- 🚫 禁用括號、冒號等特殊符號
- 🔒 反映內容本質，核心概念優先

#### B. YAML前置資料（必須完整）
```yaml
---
title: [與檔名完全一致的標題]
created: [YYYY-MM-DD格式]
modified: [YYYY-MM-DD格式]
version: "[主版本.次版本，如1.0]"
tags: [必須包含四大類標籤]
aliases: [至少2個相關別名：同義詞、簡稱、相關術語]
---
```

#### C. 四大類標籤系統（必須完整）
主題標籤（1-3個，必須二級階層）：
- 格式：`主題/一級/二級`
- 一級：科學、技術、人文、生活、健康
- 二級：如技術/程式設計、科學/物理
- 🚫 禁用一級標籤（如僅`主題/技術`）

類型標籤（必選1個）：
- `類型/概念`：理論解釋、學術知識
- `類型/指南`：實用操作、教學內容
- `類型/筆記`：知識整理、個人記錄
- `類型/資料`：參考資料、數據整理
- `類型/分析`：深度評論、現象解讀

狀態標籤（必選1個）：
- `狀態/已完成`：內容完整，無需編輯
- `狀態/進行中`：正在編輯或更新
- `狀態/草稿`：初步整理，需完善

備註標籤（條件必須，0-2個）：
- AI生成>50%：必須添加`備註/AI生成`
- 時效性內容：必須添加`備註/時效性`
- 其他：`備註/個人系統`、`備註/學習資源`、`備註/待驗證`、`備註/矛盾`、`備註/可信度存疑`

#### D. 內容結構（必須包含）
1. 彙整者備註區（必須存在）：
```markdown
> [!note] 彙整者備註:
> [初始為空，供用戶添加個人註解]
```

2. 導航系統（按長度觸發）：
- 500字+：必須有TL;DR摘要
- 2500字+：必須有章節樹導航

3. 章節樹格式（統一格式A）：
```markdown
## 📋 章節導航
- [[#主要章節一|主要章節一]]
    - [[#子章節1.1|子章節1.1]]
    - [[#子章節1.2|子章節1.2]]
- [[#主要章節二|主要章節二]]
    - [[#子章節2.1|子章節2.1]]
    - [[#子章節2.2|子章節2.2]]
```

4. 參考資料章節（條件必須）：
當有URL來源時必須添加：
```markdown
## 📚 參考資料
- [資料來源標題](URL) - 簡要描述
- [影片標題](YouTube URL) - 課程/講座描述
```

### 標題層級規範
- H1層級：僅用於筆記主標題（文件最上方）
- H2層級：內容結構從這裡開始，主要章節
- H3層級：子章節，H2下的細分內容
- H4層級及以下：避免過度嵌套，最多使用到H4
- 為主要章節添加表情符號圖標（如`## 📚 相關資源`）

### 視覺增強技術
標記元素運用：
- Callout區塊：使用`> [!note]`、`> [!tip]`、`> [!warning]`、`> [!conflict]`等標記重要信息
- 摺疊區塊：使用`<details>`隱藏次要或進階內容
- 引用區塊：使用`>`強調關鍵觀點
- 水平分隔線：使用`---`分隔主要內容區塊
- 表情符號圖標：為主要章節添加視覺標識

格式化策略：
- 表格組織：使用表格組織比較性或多維度信息
- 粗體標記：使用**粗體**標記關鍵概念和重要術語
- 斜體突出：使用*斜體*突出次要強調內容
- 術語解釋：重要術語首次出現時提供簡要解釋
- 具體實例：針對抽象概念提供具體實例或類比

列表應用原則：
- 有序列表：過程性內容使用有序列表
- 無序列表：特徵、要點等使用無序列表
- 嵌套限制：避免過度嵌套（最多兩層）
- 任務列表：使用`- [ ]`標記行動項目

### 禁止事項（絕對不可違背）
1. 禁止創建不存在的連結：
- 不得假設其他檔案存在
- 不得生成範例參考資料連結
- 內部連結僅用於文件內章節導航

2. 禁止省略必要元素：
- YAML前置資料必須完整
- 四大類標籤必須齊全（備註標籤視內容而定）
- 彙整者備註區必須存在
- 參考資料章節（當有來源URL時）

3. 禁止格式不一致：
- 章節樹格式必須統一
- 標籤命名必須規範
- 前綴使用必須正確
- 標題層級必須邏輯正確

4. 禁止過度裝飾：
- 視覺元素適量使用，避免過度裝飾
- 結構化改進不應破壞內容流暢性
- 根據筆記長度與複雜度調整格式化程度

## PROCESSING_GUIDELINES

### 核心格式檢查清單（必須100%執行）
□ 檔案名稱符合前綴-核心概念格式（12-18字最佳）
□ title與檔案名完全一致
□ YAML前置資料完整（6個必要欄位）
□ 包含完整四大類標籤
□ 主題標籤使用二級階層（禁止一級標籤）
□ 類型標籤僅選擇一個標準類型
□ 狀態標籤使用標準用詞
□ aliases至少包含2個相關別名
□ 彙整者備註區塊存在
□ 內部連結僅用於章節導航

### 擴展格式檢查清單（條件觸發）
□ AI生成內容>50%時添加備註/AI生成標籤
□ 時效性內容添加備註/時效性標籤
□ 500字以上內容包含TL;DR摘要
□ 2500字以上內容包含章節樹導航
□ 有URL來源時包含參考資料章節
□ 標題層級邏輯正確（H1僅用於主標題）
□ 視覺增強元素適當使用
□ 格式一致性檢查通過

### 批量處理格式一致性
批量處理規則：
- 相同主題筆記：使用一致的主題標籤和前綴選擇
- 系列內容：保持結構模板和視覺元素統一
- 版本更新：自動遞增version號，保持命名規範
- 標籤統一：同一批次使用統一的標籤體系

一致性檢查：
- 命名一致性：同類內容使用相同前綴和命名模式
- 結構一致性：相似長度內容使用相同結構模板
- 視覺一致性：同一主題使用相同的視覺增強策略

### 內容與風格平衡原則
內容完整性：
- 保持原始信息完整，不刪減關鍵內容
- 維持內容語氣與風格的一致性
- 優先確保內容的準確性與清晰度

實用與美觀平衡：
- 結構化改進不應破壞內容流暢性
- 視覺元素適量使用，避免過度裝飾
- 根據筆記長度與複雜度調整格式化程度

## OUTPUT_SPECIFICATIONS
"將以下內容進行Obsidian格式標準化，嚴格遵守所有強制性規範，確保檔案命名、YAML、標籤、結構、視覺增強等方面的完全規範化：[PROMPT]"
```

---

## 📋 使用流程

### 標準流程（推薦）
1. **資訊組織**: 使用通用資訊組織引擎處理任意內容
2. **格式規範**: 使用Obsidian格式規範引擎進行標準化
3. **最終檢查**: 確認兩個引擎處理結果無衝突

### 快速總結流程
1. **對話整理**: 使用通用資訊組織引擎的SUMMARY_MODE
2. **結構化輸出**: 直接獲得邏輯清晰的知識回顧

### 批量處理流程
1. **多筆記合併**: 使用Obsidian格式規範引擎的批量模式
2. **一致性保證**: 確保格式統一和結構一致
