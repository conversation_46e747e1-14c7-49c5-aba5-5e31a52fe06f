# obsidian_information_integration_engine_v2

**Version Control:**
- Version: 2.0 (整合content_summarizing_organizer的雙模式版)
- Created: 2025-06-25
- Purpose: 整合Obsidian筆記資訊處理與對話串整理功能，提供完整處理和概要整理兩種模式
- Features: 雙模式操作、智能模式選擇、完整功能保留

````
# Obsidian資訊統整引擎 v2.0

## PURPOSE
整合Obsidian筆記資訊處理與對話串整理功能，提供完整處理模式（標準Obsidian筆記）和概要整理模式（對話串概要）。與格式規範引擎配合實現完整筆記優化。

## CORE_FRAMEWORK

### 🔧 MODE_SELECTION_PROTOCOL
**智能模式選擇**：
```
輸入特徵分析 → 模式選擇：

"對話|討論|會議|交流" + "整理|總結|回顧" → SUMMARY_MODE
"筆記|文檔|資料" + "標準化|規範化|整合" → COMPREHENSIVE_MODE  
"快速|概要|簡要" → SUMMARY_MODE
"完整|詳細|深度" → COMPREHENSIVE_MODE
```

**手動模式指定**：
- COMPREHENSIVE_MODE：完整Obsidian筆記處理
- SUMMARY_MODE：對話串概要整理
- AUTO_MODE：根據輸入特徵自動選擇

## COMPREHENSIVE_MODE_FRAMEWORK

### 📚 知識連接方法

#### 1. 內部連結策略
**內部連結僅限兩種用途**：
- 文件內章節導航
- 已存在文件連結（🚫 不假設創建不存在文件）
- 使用章節樹實現快速跳轉
- 🚫 不創建"相關筆記"章節，避免混淆已有與假設條目

#### 2. 筆記關聯系統
- 識別並標記主題MOC、專案MOC或總覽MOC適用場景
- 區分理論知識與實踐應用部分
- 理論概念連結至實際案例，實用技巧補充理論基礎
- 區分強關聯和弱關聯，突出核心連結

#### 3. 概念關聯建立
- 理論知識 ↔ 實踐應用：建立雙向連結
- 抽象概念 → 具體實例：提供實際案例說明
- 主題MOC識別：標記總覽型筆記適用場景
- 強關聯vs弱關聯：區分核心連結和次要連結

### 🔍 內容類型識別決策樹
```
關鍵詞識別 → 內容類型 → 處理策略

"引用|參考文獻|研究|論文|期刊" → 學術文獻 → 保留引用格式
"課程|講座|教學|學習" → 課程筆記 → 按主題分區
"專案|計劃|目標|任務" → 專案計劃 → 任務列表化
"閱讀|書評|摘要|總結" → 閱讀摘要 → 觀點區分
"教程|步驟|操作|指南" → 教程內容 → 步驟列表化
"討論|對話|會議|交流" → 討論串 → 保留脈絡
"方法|流程|程序|步驟" → 方法流程 → 決策點突出
"概念|理論|定義|原理" → 概念理論 → 實例補充
"資料|數據|統計|清單" → 參考資料 → 結構化組織
"筆記|記錄|整理|心得" → 學習內容 → 重點突出
```

### 📝 筆記類型處理指南

#### 1. 學術與文獻筆記
- 保留學術引用格式與完整參考文獻
- 標記研究方法與數據來源
- 清晰區分原始內容與個人分析
- 提取關鍵引述並標明出處頁碼

#### 2. 課程筆記
- 按講座或主題明確分區
- 突出關鍵學習點與概念
- 添加實例與應用場景
- 創建問題-答案對

#### 3. 專案計劃
- 清晰區分目標、步驟與資源
- 使用任務列表(`- [ ]`)標記行動項目
- 建立時間線與優先級標記
- 注明先決條件與預期結果

#### 4. 閱讀摘要
- 在開頭提供簡明概述
- 使用引用區塊標記原文引述
- 清晰區分作者觀點與個人思考
- 提取核心論點與支持證據

#### 5. 教程內容
- 添加實踐檢查表與行動步驟
- 提供簡明的流程摘要
- 突出常見問題與解決方案
- 明確標記必要步驟與可選步驟

#### 6. 討論串記錄
- 保留對話脈絡和時間順序
- 突出關鍵結論與洞見
- 合併相似觀點，標記分歧點
- 標記未解決問題和待討論事項

#### 7. 方法與流程
- 創建清晰步驟列表
- 突出關鍵決策點
- 注明先決條件與預期結果
- 使用並列格式比較不同方法

#### 8. 概念與理論
- 提供簡明定義和核心原理
- 列舉應用場景和實際案例
- 比較相似概念的異同
- 建立概念間的邏輯關係

### 🎯 場景識別與適應策略

#### 場景識別觸發條件
```
輸入特徵 → 場景類型 → 處理策略

"單一文件 + 基礎整理需求" → 資料轉筆記 → 輕量處理
"現有筆記 + 格式不統一" → 筆記重整標準化 → 結構保持
"多個文件 + 合併需求" → 多筆記整合 → 深度統合
"對話記錄 + 多個筆記引用" → 討論串連結 → 關聯建議
```

#### 1. 資料轉筆記場景
- 專注於基礎結構優化與內容清晰呈現
- 輕量使用知識連接功能，主要關注關鍵術語
- 使用簡單扼要的別名和基礎標籤
- 保持原始信息完整，不刪減關鍵內容

#### 2. 筆記重整與標準化場景
- 保留原始創建日期，更新修改日期
- 維持原始筆記核心結構和主要內容組織
- 標準化標題層級和格式，確保一致性
- 將現有標籤系統重新組織為四大功能分類標籤
- 更新內部連結以符合「僅連接實際存在」原則
- 添加適當視覺元素增強但不過度改變原文風格
- 創建符合標準的章節樹或目錄，便於導航
- 視需要添加TL;DR摘要提高快速理解能力

#### 3. 多筆記整合場景（單純合併）
- 統一並深化別名和標籤體系
- 建立統一的主標題與大綱，反映整合後內容範圍
- 按主題或邏輯順序組織各筆記內容，而非簡單堆疊
- 消除重複內容，保留表述最完整或準確版本
- 識別並調和矛盾觀點，必要時並列呈現不同立場
- 創建統一標籤系統，將原筆記標籤合併為四大類
- 在章節之間建立清晰過渡，維持整體流暢性
- 添加章節間內部連結，增強導航性
- 在開頭提供整合筆記總體概述，說明來源和範圍

#### 4. 討論串筆記連結建議
- 當討論串內存在多個筆記或md檔案實例時：
  * 識別常見概念、術語或主題，建議可能的概念連結
  * 分析不同筆記間關聯性，提出連結建議
  * 識別可能應合併的相似內容，提出簡化建議
  * 在筆記結尾「連結建議」部分呈現這些建議
  * 建議連結時具體指明來源筆記和目標位置
  * 區分「強關聯」和「弱關聯」，突出核心連結
- 連結建議應具體而非籠統，例如：
  * "概念A在筆記X和筆記Y中都有討論，建議建立連結"
  * "筆記Z的方法可應用於筆記W的問題情境"

### 🔧 內容衝突與重複處理

#### 1. 重複內容處理
- 抽象出公因數（將共同基礎知識提取為獨立部分）
- 識別包含關係（合併完全重疊的內容）
- 採用聯集策略（保留所有獨特內容，去除重複部分）
- 使用表格呈現略有差異的內容版本

#### 2. 衝突內容解決
- 客觀資訊衝突：評估可信度，選擇更權威資料
- 主觀觀點衝突：呈現不同立場，保持中立
- 無法解決的矛盾：使用`> [!conflict]`標示
- 時效性衝突：標記時間，保留最新資訊

#### 3. 版本管理方法
- 在YAML前置資料中記錄版本信息
- 在筆記底部加入更新日誌（若需要）
- 使用摺疊區塊保存重要的舊版內容

#### 4. 矛盾處理策略
- 識別矛盾類型：事實矛盾vs觀點分歧
- 評估資料來源可信度
- 標記不確定或存疑的內容
- 保留多元觀點，避免強制統一

### 📊 內容品質提升策略

#### 1. 內容深化技術
- 概念解釋深化：為抽象概念提供具體實例或類比
- 實例補充：理論知識配合實際案例
- 背景補充：為專業術語提供必要背景知識
- 關聯建立：建立概念間的邏輯關係

#### 2. 結構優化技術
- 邏輯順序調整：按邏輯關係重新組織內容
- 層次結構優化：建立清晰的信息層次
- 過渡連接：在章節間建立流暢過渡
- 重點突出：使用格式化突出關鍵信息

#### 3. 可讀性提升技術
- 段落重組：將過長段落分割為合理部分
- 術語統一：確保同一概念使用一致術語
- 表達簡化：將複雜表達轉換為清晰易懂的形式
- 導航增強：提供清晰的內容導航

## SUMMARY_MODE_FRAMEWORK

### 📋 六步驟整理法

#### 1. 主題概述
用2-3句話簡述討論的主要領域或主題範圍

#### 2. 邏輯分類
按照知識的邏輯關係分成幾個主要類別，採用章節形式組織

#### 3. 概念列舉
在每個類別下列出討論過的具體概念、理論或技術要點

#### 4. 要點回顧
用簡潔但完整的語言回顧每個要點的核心內容，包含足夠訊息以快速回憶討論內容，但不重述所有細節

#### 5. 關聯標註
特別標註出概念之間的邏輯關係和相互影響

#### 6. 學習路徑
如果討論內容豐富，提供建議的學習路徑或深入方向。內容較少時可省略此項

### 📐 結構組織原則
保持清晰的層次結構，使用適當的標題分級。確保每個概念的表述具有獨立性和完整性。重視概念間的邏輯連結，避免孤立的知識點。

### 📏 內容深度控制
平衡詳細程度與簡潔性，提供足夠回憶討論的訊息但避免冗長重述。識別核心概念和支撐細節，突出重要的知識結構。

### 🔗 關聯性標註方法
使用箭頭（→）標示因果關係和推導過程。用關鍵詞標註關聯類型：前置知識、應用場景、對比概念、延伸思考。

### 💬 互動式結尾
結尾詢問是否需要對特定部分進行更深入的回顧或澄清。

## PROCESSING_GUIDELINES

### 🔒 雙模式檢查清單

#### COMPREHENSIVE_MODE檢查（10項）
```markdown
□ 根據內容類型應用相應處理指南
□ 處理重複和衝突內容
□ 根據使用場景調整處理策略
□ 建立適當的知識連接
□ 識別並標記MOC適用場景
□ 區分理論知識與實踐應用
□ 提供具體實例和案例
□ 建立概念間邏輯關係
□ 處理矛盾和衝突觀點
□ 優化內容結構和邏輯順序
```

#### SUMMARY_MODE檢查（6項）
```markdown
□ 主題概述簡潔明確（2-3句話）
□ 邏輯分類合理清晰
□ 概念列舉完整準確
□ 要點回顧平衡詳簡
□ 關聯標註清楚有效
□ 學習路徑建議實用（如適用）
```

### 🚫 雙模式禁止事項
- 創建不存在的文件連結
- 生成範例參考資料連結
- 簡單堆疊多筆記內容而不進行整合
- 忽略內容類型的特殊處理需求
- 刪減關鍵內容信息
- 強制統一矛盾觀點
- 過度簡化複雜概念
- 破壞原始內容邏輯結構

### 📈 品質提升原則
1. **內容完整性優先**：確保關鍵信息不遺失
2. **邏輯結構清晰**：建立合理的信息組織
3. **概念關聯明確**：建立有效的知識連接
4. **實用性導向**：注重實際應用價值
5. **可讀性優化**：提升理解和使用效率

## OUTPUT_SPECIFICATIONS

### COMPREHENSIVE_MODE輸出
"使用完整處理模式對以下內容進行深度資訊統整，建立知識連接，處理內容衝突，提升整體品質：[PROMPT]"

### SUMMARY_MODE輸出
"使用概要整理模式對以下對話內容進行六步驟整理，建立邏輯清晰的知識回顧架構：[PROMPT]"

### AUTO_MODE輸出
"根據內容特徵自動選擇處理模式，對以下內容進行最適合的資訊統整處理：[PROMPT]"

## USER_SUPPLEMENTARY
// 用戶可在此添加模式偏好設定
// 例如：預設模式選擇、特定內容的模式指定
// 概要深度偏好、完整處理的特殊要求
````
