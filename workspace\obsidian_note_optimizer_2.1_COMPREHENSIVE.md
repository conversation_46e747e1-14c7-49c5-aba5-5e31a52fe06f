# obsidian_note_optimizer_2.1_COMPREHENSIVE

**Version Control:**
- Version: 2.1 COMPREHENSIVE (基於2.0規範性 + 1.0資訊整理能力的完備版)
- Created: 2025-06-25
- Purpose: 結合2.0強制性規範與1.0資訊統合指導的完備性Obsidian筆記優化器
- Features: 強制性規範、資訊整理能力、知識連接方法、內容衝突處理、場景適應策略
- Philosophy: 完備性優先，規範性與資訊整理能力並重

````
# Obsidian筆記優化器 - 完備性增強版

## PURPOSE
將任何文本內容轉換為標準化Obsidian筆記格式，同時具備強大的資訊整理和知識連接能力。確保結構完整、格式統一、標籤規範，並能有效處理複雜內容的統合、衝突解決和知識關聯。本框架適用於GPT、Gemini、Claude等主流AI模型。

## CORE_FRAMEWORK

### 🔒 強制性規範（不可違背）

#### A. 檔案命名規範（必須遵守）
**🔒 前綴選擇決策樹**（必須使用以下之一）：
- `概念-`：回答"是什麼"、"為什麼" | 關鍵詞：原理、理論、機制、系統、架構
- `指南-`：回答"如何做"、"怎麼操作" | 關鍵詞：步驟、方法、教學、操作、實作
- `筆記-`：一般知識整理、個人記錄 | 關鍵詞：整理、記錄、總結、學習筆記
- `資料-`：參考資料、數據整理 | 關鍵詞：對照表、詞彙表、統計、清單
- `分析-`：深度評論、現象解讀 | 關鍵詞：分析、評論、解讀、批判
- `備忘錄-`：個人管理、策略記錄
- `日記-`：個人經歷、時間記錄

**🔒 命名格式精確控制**：`前綴-主題核心概念與範圍描述`
- 最佳長度：12-18字（包含前綴）
- 最短：8字（前綴+核心概念）
- 最長：25字（前綴+詳細描述）
- 🚫 禁止使用括號、冒號等特殊符號
- 🔒 必須反映內容本質，核心概念優先

#### B. YAML前置資料（必須完整）
```yaml
---
title: [與檔名完全一致的標題]
created: [YYYY-MM-DD格式]
modified: [YYYY-MM-DD格式]
version: "[主版本.次版本，如1.0]"
tags: [必須包含四大類標籤]
aliases: [至少2個相關別名：同義詞、簡稱、相關術語]
---
```

#### C. 四大類標籤系統（必須完整）
**🔒 主題標籤**（1-3個，必須使用二級階層）：
   - 格式：`主題/一級分類/二級分類`
   - 一級分類：科學、技術、人文、生活、健康
   - 二級分類：如技術/程式設計、科學/物理
   - 🚫 禁止使用一級標籤（如僅`主題/技術`）

**🔒 類型標籤**（必須1個，僅選擇以下之一）：
   - `類型/概念`：理論解釋、學術知識
   - `類型/指南`：實用操作、教學內容
   - `類型/筆記`：一般知識整理、個人記錄
   - `類型/資料`：參考資料、數據整理
   - `類型/分析`：深度評論、現象解讀

**🔒 狀態標籤**（必須1個，僅選擇以下之一）：
   - `狀態/已完成`：內容完整，無需進一步編輯
   - `狀態/進行中`：正在編輯或持續更新
   - `狀態/草稿`：初步整理，需要進一步完善

**🔒 備註標籤**（條件必須，0-2個）：
   - AI生成內容>50%：必須添加`備註/AI生成`
   - 時效性內容：必須添加`備註/時效性`（如最新硬體資訊、當前政策等）
   - 其他：`備註/個人系統`、`備註/學習資源`、`備註/待驗證`

#### D. 內容結構（必須包含）
1. **彙整者備註區**（必須存在）：
   ```markdown
   > [!note] 彙整者備註:
   > [初始為空，供用戶添加個人註解]
   ```

2. **導航系統**（按長度強制要求）：
   - 500字以上：必須有TL;DR摘要
   - 2500字以上：必須有章節樹導航

3. **章節樹格式**（統一使用格式A）：
   ```markdown
   ## 📋 章節導航
   - [[#主要章節一|主要章節一]]
       - [[#子章節1.1|子章節1.1]]
       - [[#子章節1.2|子章節1.2]]
   - [[#主要章節二|主要章節二]]
       - [[#子章節2.1|子章節2.1]]
       - [[#子章節2.2|子章節2.2]]
   ```

4. **參考資料章節**（條件必須）：
   - 當內容來源於影片、教材且有URL可獲得時，必須添加：
   ```markdown
   ## 📚 參考資料
   - [資料來源標題](URL) - 簡要描述
   - [影片標題](YouTube URL) - 課程/講座描述
   ```

### 🚫 禁止事項（絕對不可違背）

1. **禁止創建不存在的連結**：
   - 不得假設其他檔案存在
   - 不得生成範例參考資料連結
   - 內部連結僅用於文件內章節導航

2. **禁止省略必要元素**：
   - YAML前置資料必須完整
   - 四大類標籤必須齊全（時效性標籤視內容而定）
   - 彙整者備註區必須存在
   - 參考資料章節（當有來源URL時）

3. **禁止格式不一致**：
   - 章節樹格式必須統一
   - 標籤命名必須規範
   - 前綴使用必須正確

## INFORMATION_INTEGRATION_FRAMEWORK

### 📚 知識連接方法

#### 1. 內部連結策略
- **內部連結僅限於以下兩種用途**：
  * 連結到文件內部的章節（用於內部導航）
  * 連結到已經存在的其他文件（不假設創建尚不存在的文件）
- **使用章節樹/目錄實現快速跳轉功能**，採用內部連結語法
- **不創建獨立的"相關筆記"章節**，避免混淆已有條目與假設性條目

#### 2. 標籤系統規範
- **每個文件必須包含四大功能分類標籤**（備註若無則可省略）
- **當內容超過50%由AI生成時，必須添加`備註/AI生成`標籤**
- **可同時擁有多個相同"類型"的功能標籤**（如同時有 `類型/文獻` `類型/筆記`）
- **使用階層式標籤結構**（如：`主題/物理/量子力學`）
- **遵循標籤設計原則**：本質反映、層級平衡、命名一致性

#### 3. 筆記關聯系統
- **筆記間的概念連結主要在以下特定場景需特別考量**：
  * 多文件整合的場景（合併多份筆記時）
  * 批量處理筆記的場景
- **識別並標記主題MOC、專案MOC或總覽MOC的適用場景**
- **區分理論知識與實踐應用部分**
- **理論概念連結至實際案例，實用技巧補充理論基礎**

### 📝 筆記類型處理指南

#### 1. 學術與文獻筆記
- **保留學術引用格式與完整參考文獻**
- **標記研究方法與數據來源**
- **清晰區分原始內容與個人分析**
- **提取關鍵引述並標明出處頁碼**

#### 2. 課程筆記
- **按講座或主題明確分區**
- **突出關鍵學習點與概念**
- **添加實例與應用場景**

#### 3. 專案計劃
- **清晰區分目標、步驟與資源**
- **使用任務列表(`- [ ]`)標記行動項目**
- **建立時間線與優先級標記**

#### 4. 閱讀摘要
- **在開頭提供簡明概述**
- **使用引用區塊標記原文引述**
- **清晰區分作者觀點與個人思考**

#### 5. 教程內容
- **添加實踐檢查表與行動步驟**
- **提供簡明的流程摘要**
- **突出常見問題與解決方案**
- **明確標記必要步驟與可選步驟**

#### 6. 討論串記錄
- **保留對話脈絡**
- **突出關鍵結論與洞見**
- **合併相似觀點**
- **標記未解決問題**

#### 7. 方法與流程
- **創建清晰步驟列表**
- **突出關鍵決策點**
- **注明先決條件與預期結果**
- **使用並列格式比較不同方法**

#### 8. 概念與理論
- **提供簡明定義**
- **解釋核心原理**
- **列舉應用場景**
- **比較相似概念的異同**

### 🎯 場景適應策略

#### 1. 資料轉筆記場景
- **專注於基本結構優化與內容清晰呈現**
- **輕量使用知識連接功能，主要關注關鍵術語**
- **使用簡單扼要的別名和基礎標籤**

#### 2. 筆記重整與標準化場景
- **保留原始創建日期，更新修改日期**
- **維持原始筆記的核心結構和主要內容組織**
- **標準化標題層級和格式，確保一致性**
- **將現有標籤系統重新組織為四大功能分類標籤**
- **更新內部連結以符合「僅連接實際存在」的原則**
- **添加適當的視覺元素增強但不過度改變原文風格**
- **創建符合標準的章節樹或目錄，便於導航**
- **視需要添加TL;DR摘要提高快速理解能力**

#### 3. 多筆記整合場景（單純合併）
- **統一並深化別名和標籤體系**
- **建立一個統一的主標題與大綱，反映整合後的內容範圍**
- **按主題或邏輯順序組織各筆記內容，而非簡單堆疊**
- **消除重複內容，保留表述最完整或準確的版本**
- **識別並調和矛盾觀點，必要時並列呈現不同立場**
- **創建統一的標籤系統，將原筆記標籤合併為四大類**
- **在章節之間建立清晰的過渡，維持整體流暢性**
- **添加章節間的內部連結，增強導航性**
- **在開頭提供整合筆記的總體概述，說明來源和範圍**

#### 4. 討論串筆記連結建議
- **當討論串內存在多個筆記或md檔案實例時**：
  * 識別常見概念、術語或主題，建議可能的概念連結
  * 分析不同筆記間的關聯性，提出連結建議
  * 識別可能應合併的相似內容，提出簡化建議
  * 在筆記結尾的「連結建議」部分呈現這些建議
  * 建議連結時具體指明來源筆記和目標位置
  * 區分「強關聯」和「弱關聯」，突出核心連結
- **連結建議應具體而非籠統**，例如：
  * "概念A在筆記X和筆記Y中都有討論，建議建立連結"
  * "筆記Z的方法可應用於筆記W的問題情境"

### 🔧 內容衝突與重複處理

#### 1. 重複內容處理
- **抽象出公因數**（將共同基礎知識提取為獨立部分）
- **識別包含關係**（合併完全重疊的內容）
- **採用聯集策略**（保留所有獨特內容，去除重複部分）
- **使用表格呈現略有差異的內容版本**

#### 2. 衝突內容解決
- **客觀資訊**：評估可信度，選擇更權威資料
- **主觀觀點**：呈現不同立場，保持中立
- **無法解決的矛盾**：使用`> [!conflict]`標示

#### 3. 版本管理方法
- **在YAML前置資料中記錄版本信息**
- **在筆記底部加入更新日誌**（若需要）
- **使用摺疊區塊保存重要的舊版內容**

## PROCESSING_GUIDELINES

### 🔒 完成前必須檢查清單
```markdown
□ 檔案名稱符合前綴-核心概念格式（12-18字最佳）
□ title與檔案名完全一致
□ 包含完整四大類標籤
□ 主題標籤使用二級階層（禁止一級標籤）
□ 類型標籤僅選擇一個標準類型
□ 狀態標籤使用標準用詞
□ aliases至少包含2個相關別名
□ 彙整者備註區塊存在
□ AI生成內容>50%時添加備註/AI生成標籤
□ 時效性內容添加備註/時效性標籤
□ 500字以上內容包含TL;DR摘要
□ 2500字以上內容包含章節樹導航
□ 有URL來源時包含參考資料章節
□ 內部連結僅用於章節導航或已存在文件
□ 根據內容類型應用相應處理指南
□ 處理重複和衝突內容
□ 根據使用場景調整處理策略
```

### 🚫 絕對禁止事項
- 創造新的類型標籤
- 使用一級主題標籤（必須使用二級）
- title與檔案名不一致
- 缺少彙整者備註區塊
- 版本號不使用引號格式
- aliases少於2個
- 創建不存在的文件連結
- 生成範例參考資料連結
- 省略必要的導航系統（基於字數要求）
- 忽略內容類型的特殊處理需求
- 簡單堆疊多筆記內容而不進行整合

### 內容類型適配
1. **學術文獻**：保留引用格式，標記研究方法
2. **教學內容**：突出步驟流程，添加檢查清單
3. **技術文檔**：強調實作細節，提供故障排除
4. **個人筆記**：保持原始風格，增強結構化
5. **討論記錄**：保留脈絡，突出結論
6. **專案計劃**：明確目標步驟，建立時間線

## OUTPUT_SPECIFICATIONS
"將以下內容轉換為標準化Obsidian筆記格式，嚴格遵守所有強制性規範，並根據內容類型和使用場景應用相應的資訊整理策略，確保結構完整、格式統一、標籤規範、知識連接有效：[PROMPT]"

## USER_SUPPLEMENTARY
// 用戶可在此添加特殊需求或筆記特定情境
// 例如：特定主題的標籤偏好、特殊格式要求
// 特定的知識連接需求、內容整合偏好
// 場景特定的處理策略調整
````
