# Obsidian Project Index

**Purpose**: 橫向連結索引，包含所有筆記的檔名、標題、標籤、TL;DR等關鍵信息，用於快速查找和建立連結。

---

## 📋 Core Tools

### universal_information_organizer
- **File**: `universal_information_organizer.md`
- **Title**: 通用資訊組織引擎
- **Tags**: `#工具/資訊處理` `#類型/引擎` `#狀態/已完成` `#備註/核心工具`
- **TL;DR**: 通用資訊精煉與統合工具，提供深度組織模式（完整資訊統合）和快速總結模式（對話串整理），適用於任何文本內容，不限特定格式。整合了知識連接、內容類型處理、場景適應、衝突解決等功能。
- **Key Features**: 雙模式操作、10種內容類型處理、4種場景適應、智能模式選擇
- **Use Cases**: 文章整理、新聞分析、報告統合、對話串總結

### obsidian_format_standardization_engine
- **File**: `obsidian_format_standardization_engine.md`
- **Title**: Obsidian格式規範引擎
- **Tags**: `#工具/格式規範` `#類型/引擎` `#狀態/已完成` `#備註/Obsidian專用`
- **TL;DR**: 專門負責Obsidian筆記格式標準化：檔案命名、YAML、四大類標籤、內容結構、視覺增強的完全規範化。包含核心檢查清單和批量處理功能。
- **Key Features**: 強制性規範、視覺增強、批量處理、一致性保證
- **Use Cases**: Obsidian筆記格式化、批量標準化、格式一致性檢查

---

## 📚 Documentation & Analysis

### obsidian_dual_engine_collaboration_guide
- **File**: `obsidian_dual_engine_collaboration_guide.md`
- **Title**: Obsidian雙引擎協作指南
- **Tags**: `#文檔/使用指南` `#類型/指南` `#狀態/已完成` `#備註/協作機制`
- **TL;DR**: 指導格式規範引擎和資訊統整引擎的正確組合使用，確保協同工作、避免衝突、實現最佳筆記優化效果。提供3種使用流程和完整衝突解決機制。
- **Key Features**: 功能邊界劃分、使用流程指導、衝突解決機制
- **Use Cases**: 雙引擎組合使用、工作流程設計、問題排除

### 功能完整性檢查報告
- **File**: `功能完整性檢查報告.md`
- **Title**: 功能完整性檢查報告
- **Tags**: `#文檔/分析報告` `#類型/分析` `#狀態/已完成` `#備註/品質保證`
- **TL;DR**: 驗證三個來源功能的100%保留，確認語句壓縮的無損效果，評估整合後工具體系的完整性和實用性。包含詳細的功能對照和壓縮效果分析。
- **Key Features**: 功能完整性驗證、語句壓縮效果檢查、整合效果評估
- **Use Cases**: 品質驗證、功能確認、效果評估

### 功能合併評估分析
- **File**: `功能合併評估分析.md`
- **Title**: 功能合併評估分析
- **Tags**: `#文檔/評估分析` `#類型/分析` `#狀態/已完成` `#備註/決策支援`
- **TL;DR**: 分析obsidian_information_integration_engine與content_summarizing_organizer的功能重疊度（60%）和互補性，評估合併可行性，提出雙模式整合方案。
- **Key Features**: 功能重疊分析、合併風險評估、整合策略設計
- **Use Cases**: 決策支援、風險評估、策略規劃

### 引擎功能完備性分析
- **File**: `引擎功能完備性分析.md`
- **Title**: 引擎功能完備性分析
- **Tags**: `#文檔/功能分析` `#類型/分析` `#狀態/已完成` `#備註/完備性檢查`
- **TL;DR**: 檢查兩個引擎是否涵蓋所有1.0-2.0-2.1版本功能，識別遺漏功能和邏輯問題，提出改進建議。當前完備性85%，需補強5個關鍵點。
- **Key Features**: 功能覆蓋度檢查、遺漏功能識別、邏輯問題分析
- **Use Cases**: 完備性驗證、問題識別、改進指導

---

## 🔄 Legacy & Archive

### obsidian_note_optimizer_3.0_ULTIMATE
- **File**: `obsidian_note_optimizer_3.0_ULTIMATE.md`
- **Title**: obsidian_note_optimizer_3.0_ULTIMATE
- **Tags**: `#工具/筆記優化` `#類型/概念` `#狀態/封存` `#備註/實驗版本`
- **TL;DR**: 基於meta-prompt優化經驗的終極版Obsidian筆記優化器，採用三層分離架構，解決命名域混淆問題。Token目標1200-1800，包含問題導向設計和實戰驗證簡化。
- **Key Features**: 三層分離架構、問題導向設計、token效率優化
- **Use Cases**: 實驗參考、架構研究、歷史版本

### obsidian_note_optimizer_2.1_COMPREHENSIVE
- **File**: `obsidian_note_optimizer_2.1_COMPREHENSIVE.md`
- **Title**: obsidian_note_optimizer_2.1_COMPREHENSIVE
- **Tags**: `#工具/筆記優化` `#類型/概念` `#狀態/封存` `#備註/完備版`
- **TL;DR**: 結合2.0強制性規範與1.0資訊統合指導的完備性Obsidian筆記優化器。包含強制性規範、資訊整理能力、知識連接方法、內容衝突處理、場景適應策略。
- **Key Features**: 完備性優先、規範性與資訊整理並重
- **Use Cases**: 功能參考、完備性研究、版本演進

### obsidian_information_integration_engine_v2
- **File**: `obsidian_information_integration_engine_v2.md`
- **Title**: obsidian_information_integration_engine_v2
- **Tags**: `#工具/資訊統整` `#類型/引擎` `#狀態/封存` `#備註/整合版本`
- **TL;DR**: 整合content_summarizing_organizer的雙模式版資訊統整引擎，提供完整處理模式（標準Obsidian筆記）和概要整理模式（對話串概要）。
- **Key Features**: 雙模式操作、智能模式選擇、功能整合
- **Use Cases**: 整合參考、模式設計、功能演進

---

## 📊 Quick Reference

### File Count: 9
### Active Tools: 2
### Documentation: 4  
### Archive: 3

### Primary Workflow:
```
任意內容 → universal_information_organizer → obsidian_format_standardization_engine → 標準筆記
```

### Secondary Workflows:
```
對話串 → universal_information_organizer(SUMMARY_MODE) → 結構化總結
多筆記 → obsidian_format_standardization_engine(批量模式) → 合併筆記
```

---

## 🔗 Cross-Reference Matrix

| Tool | Related Files | Dependencies | Output Format |
|------|---------------|--------------|---------------|
| universal_information_organizer | collaboration_guide | None | 任何格式 |
| obsidian_format_standardization_engine | collaboration_guide | None | Obsidian格式 |
| collaboration_guide | Both engines | Both tools | 使用指導 |

---

## 📝 Update Log
- 2025-06-25: 初始創建，包含9個文件的完整索引
- 2025-06-25: 確認功能完整性100%保留
- 2025-06-25: 完成工具簡化（3→2個核心工具）
