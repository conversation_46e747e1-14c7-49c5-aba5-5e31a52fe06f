# obsidian_dual_engine_collaboration_guide

**Version Control:**
- Version: 1.0 (雙引擎協作指南)
- Created: 2025-06-25
- Purpose: 指導如何組合使用格式規範引擎和資訊統整引擎，實現最佳的筆記優化效果

````
# Obsidian雙引擎協作指南

## PURPOSE
指導格式規範引擎和資訊統整引擎的正確組合使用，確保協同工作、避免衝突、實現最佳筆記優化效果。

## CORE_FRAMEWORK

### 🔧 引擎功能邊界劃分

#### 格式規範引擎負責：
- 📁 檔案命名標準化
- 📋 YAML前置資料完整性
- 🏷️ 四大類標籤系統規範
- 📐 標題層級和結構規範
- 🎨 視覺增強元素應用
- 📏 基礎格式一致性檢查

#### 資訊統整引擎負責：
- 📚 知識連接和關聯建立
- 📝 內容類型專門處理
- 🎯 場景適應策略應用
- 🔧 內容衝突和重複解決
- 📊 內容品質深度提升
- 🔗 筆記間關係建立

### 🔄 推薦使用流程

#### 流程A：標準優化流程（推薦）
```
步驟1: 格式規範引擎
- 執行檔案命名、YAML、標籤、基礎結構規範化
- 應用視覺增強和格式標準化
- 完成核心格式檢查清單

步驟2: 資訊統整引擎
- 識別內容類型並應用專門處理
- 建立知識連接和內容關聯
- 處理衝突和重複內容
- 根據場景調整處理策略

步驟3: 最終檢查
- 確認兩個引擎的處理結果無衝突
- 驗證整體品質達到預期標準
```

#### 流程B：快速格式化流程
```
適用場景: 僅需要格式標準化，不需要深度內容處理
使用引擎: 僅使用格式規範引擎
適用情況: 格式混亂但內容已經組織良好的筆記
```

#### 流程C：深度整合流程
```
適用場景: 複雜內容需要深度處理和整合
使用引擎: 兩個引擎 + 多輪迭代
適用情況: 多筆記合併、討論串整理、複雜內容統合
```

### ⚖️ 衝突解決機制

#### 1. 功能重疊處理
**當兩個引擎都涉及相同功能時的處理原則**：

- **內部連結**：
  * 格式引擎：確保連結語法正確
  * 統整引擎：決定連結的邏輯合理性
  * 解決：統整引擎決定連結內容，格式引擎確保格式正確

- **視覺增強**：
  * 格式引擎：提供視覺元素的格式規範
  * 統整引擎：決定何時何處使用視覺元素
  * 解決：統整引擎決定使用時機，格式引擎確保格式統一

#### 2. 處理順序衝突
**當處理順序可能影響結果時**：

- **標題調整**：先由統整引擎確定內容結構，再由格式引擎規範標題層級
- **標籤添加**：先由格式引擎建立標籤框架，再由統整引擎根據內容調整
- **結構重組**：先由統整引擎優化邏輯結構，再由格式引擎應用格式規範

### 🎯 使用場景指導

#### 場景1：新筆記創建
```
推薦流程: A（標準優化流程）
重點: 格式規範 + 內容組織
注意: 確保從一開始就建立正確的結構和格式
```

#### 場景2：舊筆記標準化
```
推薦流程: B（快速格式化）或 A（標準優化）
選擇依據:
- 內容已組織良好 → 流程B
- 內容需要重新組織 → 流程A
```

#### 場景3：多筆記合併
```
推薦流程: C（深度整合流程）
重點: 內容統合 + 衝突解決 + 格式統一
注意: 需要多輪處理確保整合品質
```

#### 場景4：討論串整理
```
推薦流程: A（標準優化流程）+ 連結建議
重點: 內容脈絡保持 + 關聯建立 + 格式規範
特殊: 可能需要額外的連結建議處理
```

### 📋 品質檢查整合

#### 雙引擎檢查清單
```markdown
格式規範檢查（10項核心）:
□ 檔案命名規範正確
□ YAML前置資料完整
□ 四大類標籤齊全
□ 標題層級邏輯正確
□ 視覺增強適當應用
□ 內部連結格式正確
□ 基礎結構完整
□ 格式一致性達標
□ 條件觸發功能正確
□ 批量處理一致性

資訊統整檢查（10項核心）:
□ 內容類型識別正確
□ 處理策略應用適當
□ 知識連接建立有效
□ 場景適應策略正確
□ 內容衝突妥善解決
□ 重複內容有效處理
□ 內容品質顯著提升
□ 邏輯結構清晰合理
□ 可讀性明顯改善
□ 實用性價值提升
```

### ⚠️ 常見問題與解決

#### 問題1：兩個引擎處理結果不一致
**解決方案**：
- 檢查功能邊界是否清晰
- 確認處理順序是否正確
- 必要時重新執行有衝突的部分

#### 問題2：處理時間過長
**解決方案**：
- 評估是否真的需要兩個引擎
- 考慮使用快速格式化流程
- 分批處理複雜內容

#### 問題3：結果過度處理
**解決方案**：
- 檢查是否過度使用視覺增強
- 確認內容組織是否過於複雜
- 回歸簡潔性原則

## USAGE_SPECIFICATIONS

### 單引擎使用
**格式規範引擎**：
"使用格式規範引擎對以下內容進行標準化：[PROMPT]"

**資訊統整引擎**：
"使用資訊統整引擎對以下內容進行深度處理：[PROMPT]"

### 雙引擎組合使用
**標準流程**：
"先使用格式規範引擎進行標準化，再使用資訊統整引擎進行深度處理：[PROMPT]"

**深度整合**：
"使用雙引擎深度整合流程處理以下複雜內容：[PROMPT]"

## USER_SUPPLEMENTARY
// 用戶可在此添加協作偏好設定
// 例如：偏好的處理流程、特殊的衝突解決方式
// 引擎組合的個性化調整
````
