# Obsidian Optimization Prompts Collection

**Version**: 1.0 Final  
**Created**: 2025-06-25  
**Purpose**: Obsidian筆記優化相關prompt集合，包含資訊組織、格式規範、token壓縮等工具

---

## 🔧 Universal Information Organizer

**用途**: 通用資訊精煉與統合，適用任何文本內容，提供深度組織和快速總結兩種模式

```
# universal_information_organizer

## PURPOSE
通用資訊精煉與統合工具，提供深度組織模式（完整資訊統合）和快速總結模式（對話串整理）。適用於任何文本內容，不限特定格式。

## MODE_SELECTION
輸入特徵分析 → 模式選擇：
"對話|討論|會議|交流|聊天記錄" → SUMMARY_MODE
"文章|新聞|報告|論文|文檔" → ORGANIZATION_MODE
"快速|概要|簡要|總結" → SUMMARY_MODE
"深度|完整|詳細|統合" → ORGANIZATION_MODE

手動模式指定：
- ORGANIZATION_MODE：深度組織模式（完整資訊統合）
- SUMMARY_MODE：快速總結模式（對話串整理）
- AUTO_MODE：根據輸入特徵自動選擇

## ORGANIZATION_MODE_FRAMEWORK

### 知識連接方法
- 理論知識 ↔ 實踐應用：建立雙向連結
- 抽象概念 → 具體實例：提供實際案例說明
- 因果關係標註：使用箭頭（→）標示推導過程
- 強關聯vs弱關聯：區分核心連結和次要連結

### 內容類型識別決策樹
"引用|參考文獻|研究|論文|期刊" → 學術文獻 → 保留引用格式
"課程|講座|教學|學習" → 課程筆記 → 按主題分區
"專案|計劃|目標|任務" → 專案計劃 → 任務列表化
"閱讀|書評|摘要|總結" → 閱讀摘要 → 觀點區分
"教程|步驟|操作|指南" → 教程內容 → 步驟列表化
"討論|對話|會議|交流" → 討論串 → 保留脈絡
"方法|流程|程序|步驟" → 方法流程 → 決策點突出
"概念|理論|定義|原理" → 概念理論 → 實例補充
"資料|數據|統計|清單" → 參考資料 → 結構化組織
"新聞|報導|時事|事件" → 新聞資訊 → 時序整理
"筆記|記錄|整理|心得" → 學習內容 → 重點突出

### 場景適應策略
"單一文件 + 基礎整理需求" → 內容重組 → 結構優化
"現有內容 + 格式混亂" → 內容標準化 → 邏輯重整
"多個文件 + 合併需求" → 內容整合 → 深度統合
"複雜內容 + 理解困難" → 內容簡化 → 可讀性提升

### 內容衝突與重複處理
- 抽象公因數：將共同基礎知識提取為獨立部分
- 識別包含關係：合併完全重疊的內容
- 聯集策略：保留所有獨特內容，去除重複部分
- 表格呈現：略有差異的內容版本對比
- 客觀資訊衝突：評估可信度，選擇更權威資料
- 主觀觀點衝突：呈現不同立場，保持中立
- 無法解決矛盾：明確標示並說明衝突原因

### 內容品質提升策略
- 概念解釋深化：為抽象概念提供具體實例或類比
- 實例補充：理論知識配合實際案例
- 背景補充：為專業術語提供必要背景知識
- 關聯建立：建立概念間的邏輯關係
- 邏輯順序調整：按邏輯關係重新組織內容
- 層次結構優化：建立清晰的信息層次
- 過渡連接：在章節間建立流暢過渡
- 重點突出：突出關鍵信息和核心概念

## SUMMARY_MODE_FRAMEWORK

### 六步驟整理法
1. 主題概述：用2-3句話簡述討論的主要領域或主題範圍
2. 邏輯分類：按知識邏輯關係分成幾個主要類別，採用章節形式組織
3. 概念列舉：在每個類別下列出討論過的具體概念、理論或技術要點
4. 要點回顧：用簡潔但完整語言回顧每個要點核心內容，包含足夠訊息以快速回憶討論內容，但不重述所有細節
5. 關聯標註：特別標註概念之間的邏輯關係和相互影響
6. 學習路徑：如討論內容豐富，提供建議的學習路徑或深入方向。內容較少時可省略此項

### 結構組織原則
保持清晰層次結構，使用適當標題分級。確保每個概念表述具有獨立性和完整性。重視概念間邏輯連結，避免孤立知識點。

### 內容深度控制
平衡詳細程度與簡潔性，提供足夠回憶討論的訊息但避免冗長重述。識別核心概念和支撐細節，突出重要知識結構。

### 關聯性標註方法
使用箭頭（→）標示因果關係和推導過程。用關鍵詞標註關聯類型：前置知識、應用場景、對比概念、延伸思考。

### 互動式結尾
結尾詢問是否需要對特定部分進行更深入回顧或澄清。

## OUTPUT_SPECIFICATIONS
ORGANIZATION_MODE輸出：
"使用深度組織模式對以下內容進行完整資訊統合，建立知識連接，處理內容衝突，提升整體品質：[PROMPT]"

SUMMARY_MODE輸出：
"使用快速總結模式對以下對話內容進行六步驟整理，建立邏輯清晰的知識回顧架構：[PROMPT]"

AUTO_MODE輸出：
"根據內容特徵自動選擇處理模式，對以下內容進行最適合的資訊組織處理：[PROMPT]"
```

---

## 🎨 Obsidian Format Standardization Engine

**用途**: 專門負責Obsidian筆記格式標準化，包含檔案命名、YAML、標籤、結構、視覺增強

```
# obsidian_format_standardization_engine

## PURPOSE
專門負責Obsidian筆記格式標準化：檔案命名、YAML、四大類標籤、內容結構、視覺增強的完全規範化。適用於GPT、Gemini、Claude等主流AI模型。

## CORE_FRAMEWORK

### 強制性規範（不可違背）

#### A. 檔案命名規範（必須遵守）
前綴選擇決策樹（必選其一）：
- `概念-`：是什麼/為什麼 | 原理、理論、機制、系統、架構
- `指南-`：如何做/怎麼操作 | 步驟、方法、教學、操作、實作
- `筆記-`：知識整理/個人記錄 | 整理、記錄、總結、學習筆記
- `資料-`：參考資料/數據整理 | 對照表、詞彙表、統計、清單
- `分析-`：深度評論/現象解讀 | 分析、評論、解讀、批判
- `備忘錄-`：個人管理、策略記錄
- `日記-`：個人經歷、時間記錄

命名格式：`前綴-主題核心概念與範圍描述`
- 最佳：12-18字 | 最短：8字 | 最長：25字
- 🚫 禁用括號、冒號等特殊符號
- 🔒 反映內容本質，核心概念優先

#### B. YAML前置資料（必須完整）
```yaml
---
title: [與檔名完全一致的標題]
created: [YYYY-MM-DD格式]
modified: [YYYY-MM-DD格式]
version: "[主版本.次版本，如1.0]"
tags: [必須包含四大類標籤]
aliases: [至少2個相關別名：同義詞、簡稱、相關術語]
---
```

#### C. 四大類標籤系統（必須完整）
主題標籤（1-3個，必須二級階層）：
- 格式：`主題/一級/二級`
- 一級：科學、技術、人文、生活、健康
- 二級：如技術/程式設計、科學/物理
- 🚫 禁用一級標籤（如僅`主題/技術`）

類型標籤（必選1個）：
- `類型/概念`：理論解釋、學術知識
- `類型/指南`：實用操作、教學內容
- `類型/筆記`：知識整理、個人記錄
- `類型/資料`：參考資料、數據整理
- `類型/分析`：深度評論、現象解讀

狀態標籤（必選1個）：
- `狀態/已完成`：內容完整，無需編輯
- `狀態/進行中`：正在編輯或更新
- `狀態/草稿`：初步整理，需完善

備註標籤（條件必須，0-2個）：
- AI生成>50%：必須添加`備註/AI生成`
- 時效性內容：必須添加`備註/時效性`
- 其他：`備註/個人系統`、`備註/學習資源`、`備註/待驗證`、`備註/矛盾`、`備註/可信度存疑`

#### D. 內容結構（必須包含）
1. 彙整者備註區（必須存在）：
```markdown
> [!note] 彙整者備註:
> [初始為空，供用戶添加個人註解]
```

2. 導航系統（按長度觸發）：
- 500字+：必須有TL;DR摘要
- 2500字+：必須有章節樹導航

3. 章節樹格式（統一格式A）：
```markdown
## 📋 章節導航
- [[#主要章節一|主要章節一]]
    - [[#子章節1.1|子章節1.1]]
    - [[#子章節1.2|子章節1.2]]
- [[#主要章節二|主要章節二]]
    - [[#子章節2.1|子章節2.1]]
    - [[#子章節2.2|子章節2.2]]
```

4. 參考資料章節（條件必須）：
當有URL來源時必須添加：
```markdown
## 📚 參考資料
- [資料來源標題](URL) - 簡要描述
- [影片標題](YouTube URL) - 課程/講座描述
```

### 視覺增強技術
- Callout區塊：`> [!note]`、`> [!tip]`、`> [!warning]`、`> [!conflict]`
- 摺疊區塊：`<details>`隱藏次要內容
- 引用區塊：`>`強調關鍵觀點
- 表格組織：比較性或多維度信息
- 粗體標記：**關鍵概念**和重要術語
- 斜體突出：*次要強調*內容
- 有序列表：過程性內容
- 無序列表：特徵、要點
- 任務列表：`- [ ]`標記行動項目

### 禁止事項
- 禁止創建不存在的連結
- 禁止省略必要元素（YAML、標籤、彙整者備註）
- 禁止格式不一致
- 禁止過度裝飾

## OUTPUT_SPECIFICATIONS
"將以下內容進行Obsidian格式標準化，嚴格遵守所有強制性規範，確保檔案命名、YAML、標籤、結構、視覺增強等方面的完全規範化：[PROMPT]"
```

---

## 🗜️ Token Compression Framework

**用途**: 高效壓縮prompt長度，保持功能完整性，提供三級壓縮模式

```
# token_compression_framework

## PURPOSE
高效壓縮prompt token數量，在保持功能完整性前提下最大化token節省效果。提供三級壓縮模式適應不同需求。

## CORE_COMPRESSION_STRATEGIES

### 符號化標記系統
🔧 = 執行指令 | 📝 = 內容要求 | 🎯 = 輸出規範 | ⚠️ = 警告注意 | 🚫 = 禁止事項

### 條件邏輯壓縮
WHEN-DO格式: "當X情況時，執行Y" → "X ∈ Y"
EITHER-OR格式: "要麼A要麼B" → "A | B"  
AND格式: "同時A和B" → "A & B"

### 三級壓縮模式

#### Level 1: 基礎壓縮 (30-40%節省)
適用條件: 初次使用、保守安全、AI模型未知
選擇標準: 優先保證功能完整性，適度節省token
方法: 移除冗餘詞彙 + 基礎符號化
風險等級: 🟢 低風險

#### Level 2: 標準壓縮 (50-60%節省)  
適用條件: 日常使用、熟悉AI模型、追求平衡效果
選擇標準: 在功能保留和token節省間取得平衡
方法: 條件邏輯壓縮 + 結構重組
風險等級: 🟡 中風險

#### Level 3: 極致壓縮 (70%+節省)
適用條件: 專業用戶、token限制嚴格、追求極限效率
選擇標準: 最大化token節省，接受一定功能風險
方法: 全套壓縮策略 + 創新簡化
風險等級: 🔴 高風險

### 回滾與恢復機制
自動回滾觸發條件:
- 功能保留度 < 60%: 自動降級到上一級壓縮
- 符號系統不支援: 自動轉換為純文字版本
- 邏輯關係破損: 恢復關鍵連接詞和結構

手動回滾選項:
Level 3 → Level 2: 增加結構詞，降低符號密度
Level 2 → Level 1: 恢復完整句式，保留基礎壓縮
Level 1 → 原版: 完全恢復，僅移除明顯冗餘

### 效果評估與驗證
標準評估格式:
```
📊 壓縮報告
原始: [數字] tokens → 壓縮: [數字] tokens
節省: [百分比] | 功能保留: [百分比]%
風險等級: 🟢低風險/🟡中風險/🔴高風險
AI兼容性: GPT✅/Claude✅/Gemini⚠️
建議: [具體改進點或警告]
```

功能保留度計算:
評估維度 (各25%權重):
- 核心指令完整性: 是否保留主要任務要求
- 專業角色清晰度: 角色定義是否明確
- 輸出格式規範: 期望輸出是否明確
- 邏輯關係連貫: 因果關係是否清晰

### 品質保證機制
發布前必檢清單:
□ 壓縮後prompt能獨立執行（零脈絡測試）
□ 核心功能保留度 ≥ 80%
□ 目標AI模型兼容性確認
□ 變數格式正確無誤
□ 專業術語保留適當
□ 邏輯關係清晰連貫
□ 符號系統有效性驗證
□ 回滾方案準備就緒

常見失敗模式與預防:
失敗模式1: 過度符號化 → 測試AI符號支援度
失敗模式2: 邏輯關係破損 → 保留關鍵連接詞
失敗模式3: 角色定義模糊 → 確保角色描述清晰
失敗模式4: 輸出要求不明 → 保留明確輸出規範

## OUTPUT_SPECIFICATIONS
基礎壓縮: "Compress prompt using Level 1 method: [PROMPT]"
標準壓縮: "Compress prompt using Level 2 method: [PROMPT]"
極致壓縮: "Compress prompt using Level 3 method: [PROMPT]"
```

---

## 🧠 Memory Extraction Framework

**用途**: 從完成專案中系統性抽取可重複使用的記憶、洞察和知識模式

```
# memory_extraction_framework

## PURPOSE
從已完成的專案、對話或學習經驗中，系統性地抽取具有長期價值的記憶、洞察和可重複應用的知識模式。

## CORE_FRAMEWORK

### 四階段執行流程

#### Phase 1: Experience Analysis Layer
專案經驗分析（執行時間：5-10分鐘）
- 🔍 識別核心問題和解決方案
- 🔍 提取決策邏輯和權衡考量
- 🔍 記錄成功模式和失敗教訓
- 🔍 分析資源使用和效率優化

#### Phase 2: Knowledge Pattern Recognition
知識模式識別（執行時間：10-15分鐘）
- 🔍 跨案例的共同特徵識別
- 🔍 可重複應用的方法論抽取
- 🔍 領域特定vs通用原則區分
- 🔍 例外情況和邊界條件記錄

#### Phase 3: Memory Categorization System
記憶分類系統（執行時間：5-10分鐘）
記憶類型分類（互斥分類）:
- 方法論記憶: 工作流程、操作步驟、系統性方法
- 決策記憶: 判斷標準、選擇邏輯、權衡考量
- 洞察記憶: 深層理解、關鍵發現、突破性認知
- 經驗記憶: 實戰教訓、成功模式、失敗預防
- 資源記憶: 工具使用、資源配置、效率優化

#### Phase 4: Actionable Memory Generation
可行動記憶生成（執行時間：10-15分鐘）
- 🔍 轉換為具體可執行的指導原則
- 🔍 建立觸發條件和應用場景
- 🔍 設計檢驗標準和成功指標
- 🔍 預設例外處理和調整機制

### 記憶品質標準
高品質記憶特徵:
- 具體性: 有明確的操作指導
- 可重複性: 能在類似情境中重複應用
- 可驗證性: 有明確的成功/失敗判斷標準
- 適應性: 能根據情境變化進行調整
- 價值性: 對未來決策和行動有實際幫助

### 記憶組織結構
```
記憶標題: [簡潔描述核心內容]
記憶類型: [方法論/決策/洞察/經驗/資源]
適用場景: [具體的應用情境描述]
核心內容: [記憶的具體內容]
觸發條件: [何時應該想起這個記憶]
應用方法: [如何具體應用這個記憶]
注意事項: [使用時需要注意的限制或風險]
相關記憶: [與其他記憶的關聯]
```

## OUTPUT_SPECIFICATIONS
"使用記憶抽取框架對以下完成的專案進行系統性記憶抽取，識別可重複應用的知識模式和洞察：[PROMPT]"
```

---

## 📋 使用指南

### 工具組合建議
1. **新內容處理**: Universal Information Organizer → Obsidian Format Engine
2. **對話串整理**: Universal Information Organizer (SUMMARY_MODE)
3. **Prompt優化**: Token Compression Framework
4. **專案總結**: Memory Extraction Framework
5. **批量處理**: Obsidian Format Engine (批量模式)

### 選擇決策樹
```
內容類型 → 推薦工具:
"任意文本內容" → Universal Information Organizer
"Obsidian筆記格式化" → Obsidian Format Engine  
"Prompt太長需要壓縮" → Token Compression Framework
"專案完成需要總結" → Memory Extraction Framework
```
