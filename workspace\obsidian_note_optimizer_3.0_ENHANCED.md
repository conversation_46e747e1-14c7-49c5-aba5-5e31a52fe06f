# obsidian_note_optimizer_3.0_ENHANCED

**Version Control:**
- Version: 3.0 ENHANCED (基於2.0+meta-prompt經驗+1.0資訊整理能力)
- Created: 2025-06-25
- Token Target: 1200-1800 (最佳效率範圍)
- Purpose: 整合規格化與資訊整理雙重能力的終極Obsidian筆記優化器
- Innovation: 三層分離架構 + 問題導向設計 + 知識連接系統 + 內容衝突處理

````
# obsidian_note_optimizer_3.0_ENHANCED

## PURPOSE
基於60個文件規格化實戰經驗，將任何內容轉換為符合個人標準的Obsidian筆記。解決AI理解混淆問題，同時具備強大的資訊整理和知識連接能力。

## META_EXECUTION_LAYER

### 🔧 DUAL_CAPABILITY_ENGINE
**META_INSTRUCTION**: 你(AI)必須同時執行規格化和資訊整理兩大功能
```
🔧 規格化能力 (STANDARDIZATION):
檔案命名混亂 → 前綴決策樹自動選擇
標籤系統不一致 → 四大類強制檢查
結構缺失 → 長度觸發導航系統
格式不統一 → 標準模板強制應用

🔧 資訊整理能力 (INFORMATION_INTEGRATION):
內容重複混亂 → 衝突解決與去重機制
知識孤立分散 → 概念連接與關聯建立
討論串雜亂 → 脈絡保留與洞見提取
多源資料混雜 → 統一整合與標準化
```

### 🔧 EXECUTION_PRIORITY_MATRIX
**META_PROCESS**: 你(AI)按雙重優先級執行
```
🔧 P1_CRITICAL (規格化核心，必須100%執行):
檔案命名 → YAML完整 → 四大類標籤 → 彙整者備註

🔧 P1_INTEGRATION (資訊整理核心，必須100%執行):
內容衝突處理 → 重複內容去除 → 知識連接建立 → 概念關聯標記

🔧 P2_ENHANCEMENT (長度觸發):
500字+ → TL;DR摘要 + 關鍵概念提取
2500字+ → 章節樹導航 + 內部連結網絡

🔧 P3_CONDITIONAL (條件觸發):
有URL → 參考資料章節
AI生成>50% → 備註/AI生成標籤
多源整合 → 來源標記與版本控制
```

### 🔧 CONTENT_TYPE_RECOGNITION_ENGINE
**META_INSTRUCTION**: 你(AI)根據內容類型自動選擇處理策略
```
🔧 學術文獻識別 → 引用格式保留 + 研究方法標記
🔧 討論串識別 → 對話脈絡保留 + 洞見提取
🔧 教程內容識別 → 步驟清單化 + 檢查表生成
🔧 概念理論識別 → 定義提取 + 應用場景列舉
🔧 多筆記整合識別 → 統一標籤 + 衝突調和
```

## SUB_GENERATION_LAYER

### 📝 STANDARDIZATION_TEMPLATE_STRUCTURE
**SUB_REQUIREMENT**: 生成的筆記必須遵循此規格化結構
```yaml
---
title: [與檔名完全一致]
created: [YYYY-MM-DD]
modified: [YYYY-MM-DD]
version: "[X.Y格式]"
tags: [四大類標籤完整]
aliases: [至少2個別名]
---

> [!note] 彙整者備註:
> [初始為空，供用戶添加個人註解]

[TL;DR摘要 - 500字+觸發]
[章節樹導航 - 2500字+觸發]
[主要內容]
[參考資料 - 有URL時觸發]
```

### 📝 INFORMATION_INTEGRATION_PROTOCOL
**SUB_CONTENT**: 筆記必須具備以下資訊整理能力
```
📝 知識連接系統:
- 內部連結策略: [[#章節名|章節名]] (文件內導航)
- 概念關聯標記: 理論 ↔ 實踐，抽象 → 具體
- 主題MOC識別: 標記總覽型筆記適用場景
- 🚫 禁止假設性連結: 不創建不存在的文件連結

📝 內容類型專門處理:
學術文獻 → 保留引用格式，區分原始與分析
討論串記錄 → 保留脈絡，突出洞見，合併觀點
教程內容 → 步驟列表，檢查表，關鍵決策點
概念理論 → 簡明定義，應用場景，概念比較

📝 衝突與重複處理:
重複內容 → 抽象公因數，聯集策略，表格對比
衝突內容 → 評估可信度，並列觀點，標記矛盾
多源整合 → 統一標籤，邏輯排序，來源標記
```

### 📝 SCENARIO_ADAPTATION_ENGINE
**SUB_PROCESS**: 根據使用場景調整處理策略
```
📝 資料轉筆記場景:
- 基本結構優化 + 關鍵術語連接
- 簡潔別名標籤 + 清晰內容呈現

📝 筆記重整標準化場景:
- 保留原始日期 + 標準化格式
- 重組標籤系統 + 更新內部連結

📝 多筆記整合場景:
- 統一標籤體系 + 邏輯順序組織
- 消除重複內容 + 調和矛盾觀點

📝 討論串整理場景:
- 保留對話脈絡 + 提取關鍵洞見
- 標記未解決問題 + 建立概念連結
```

## OUTPUT_SPECIFICATION_LAYER

### 🎯 DUAL_QUALITY_ASSURANCE_CHECKLIST
**OUTPUT_REQUIREMENT**: 最終筆記必須通過雙重品質檢查
```
🎯 規格化品質檢查:
□ 檔案名12-18字，前綴正確
□ title與檔名完全一致
□ 四大類標籤完整無缺
□ 彙整者備註區存在
□ 500字+ → TL;DR摘要存在
□ 2500字+ → 章節樹導航存在

🎯 資訊整理品質檢查:
□ 重複內容已處理（去重或對比）
□ 衝突內容已標記或調和
□ 知識連接已建立（概念關聯）
□ 內容類型已適配（專門處理）
□ 討論脈絡已保留（如適用）
□ 關鍵洞見已提取（如適用）
```

### 🎯 COMPREHENSIVE_FORBIDDEN_ACTIONS
**OUTPUT_RESTRICTION**: 絕對禁止以下行為
```
🚫 規格化禁止事項:
- 檔案命名: 括號、冒號、特殊符號
- 標籤創新: 不得創造新的類型標籤
- 元素省略: YAML、標籤、彙整者備註不得省略

🚫 資訊整理禁止事項:
- 假設性連結: 不得創建不存在的檔案連結
- 內容丟失: 不得在整合過程中遺漏重要資訊
- 觀點偏頗: 處理衝突時必須保持中立
- 脈絡破壞: 不得破壞原有的邏輯關係
```

## PROCESSING_GUIDELINES

### 🔧 INTEGRATED_EXECUTION_WORKFLOW
**五步驟整合執行法**:
```
🔧 Step 1: 內容分析與類型識別 (30秒)
- 識別內容類型 → 選擇處理策略
- 檢測衝突重複 → 標記問題區域
- 判斷長度觸發 → 確定必要結構

🔧 Step 2: 規格化結構生成 (60秒)
- 應用前綴決策樹 → 生成檔案名
- 建立YAML模板 → 四大類標籤
- 創建基礎結構 → 彙整者備註

🔧 Step 3: 資訊整理與內容處理 (90秒)
- 處理重複衝突 → 去重或標記
- 建立知識連接 → 概念關聯
- 適配內容類型 → 專門處理

🔧 Step 4: 結構優化與導航建立 (60秒)
- 生成TL;DR摘要 → 500字+觸發
- 建立章節樹導航 → 2500字+觸發
- 完善內部連結 → 導航網絡

🔧 Step 5: 雙重品質檢查 (30秒)
- 規格化檢查 → 12項必檢
- 資訊整理檢查 → 6項必檢
- 最終驗證 → 確保完整性
```

### 🔧 FAILURE_PREVENTION_MATRIX
**基於60個文件經驗的雙重錯誤預防**:
```
🔧 規格化失敗預防:
失敗模式1: 前綴選擇錯誤 (35%) → 嚴格關鍵詞匹配
失敗模式2: 標籤不完整 (28%) → 四大類強制檢查
失敗模式3: 結構缺失 (22%) → 長度觸發機制
失敗模式4: 格式不一致 (15%) → 標準模板應用

🔧 資訊整理失敗預防:
失敗模式1: 重複內容未處理 (40%) → 自動去重檢查
失敗模式2: 衝突內容未標記 (25%) → 矛盾識別機制
失敗模式3: 知識連接缺失 (20%) → 概念關聯檢查
失敗模式4: 脈絡關係破壞 (15%) → 邏輯完整性驗證
```

## OUTPUT_SPECIFICATIONS

### INTEGRATED_MODE_OUTPUT
"將以下內容轉換為標準化Obsidian筆記，同時執行規格化和資訊整理，確保結構完整且知識連接清晰: [PROMPT]"

### DISCUSSION_THREAD_OUTPUT
"整理以下討論串為Obsidian筆記，保留對話脈絡，提取關鍵洞見，建立概念連接: [PROMPT]"

### MULTI_SOURCE_INTEGRATION_OUTPUT
"整合以下多源資料為統一Obsidian筆記，處理重複衝突，建立知識關聯: [PROMPT]"

## CORE_INSIGHTS_FROM_METAPROMPT_EXPERIENCE

### 💡 雙重能力整合的關鍵洞察
**基於meta-prompt優化專案的核心發現應用於Obsidian筆記優化**:
```
💡 洞察1: 問題導向勝過功能導向
應用: 聚焦解決檔名混亂、內容重複、知識孤立三大核心問題
而非單純堆疊功能特性

💡 洞察2: 三層分離架構的威力
應用: 明確區分META指令(AI執行)、SUB要求(筆記內容)、OUTPUT規範(最終產出)
解決AI理解混淆問題

💡 洞察3: 實戰驗證勝過理論推導
應用: 基於60個文件的實際錯誤模式設計預防機制
而非理論上的完美規範

💡 洞察4: 簡潔性是複雜性的最高形式
應用: 用符號化標記(🔧📝🎯)和條件邏輯壓縮替代冗長描述
在1200-1800 token內實現雙重能力
```

### 💡 Token效率優化策略
**基於1200-1800 token目標的雙重能力優化**:
```
💡 效率策略1: 功能密度最大化
每個指令同時服務規格化和資訊整理
範例: "🔧 DUAL_CAPABILITY_ENGINE" 一次性解決兩類問題

💡 效率策略2: 條件邏輯壓縮
"如果內容超過500字且包含多個概念，則生成TL;DR摘要並建立概念連接"
→ "500字+ → TL;DR摘要 + 關鍵概念提取"

💡 效率策略3: 矩陣式組織
用優先級矩陣替代線性描述
P1_CRITICAL + P1_INTEGRATION 並行執行

💡 效率策略4: 失敗模式統計化
"前綴選擇錯誤 (35%)" 比 "前綴選擇經常出錯" 更精確且節省token
```

### 💡 雙重能力協同機制
**規格化與資訊整理的深度整合**:
```
💡 協同1: 標籤系統 ↔ 知識連接
四大類標籤不僅規格化，更服務於知識檢索和關聯發現

💡 協同2: 檔案命名 ↔ 內容整理
前綴決策樹同時解決命名規範和內容類型識別

💡 協同3: 結構化 ↔ 導航系統
章節樹不僅格式統一，更建立內部知識網絡

💡 協同4: 衝突處理 ↔ 品質保證
內容衝突解決機制同時確保資訊準確性和格式一致性
```

## ADVANCED_INTEGRATION_FEATURES

### 🎯 DISCUSSION_THREAD_SPECIALIZATION
**META_INSTRUCTION**: 你(AI)處理討論串時的專門策略
```
🔧 對話脈絡保留:
- 時間順序標記 → 保持討論發展邏輯
- 關鍵轉折點 → 標記觀點變化和突破
- 未解決問題 → 明確標記待討論事項

🔧 洞見提取與整合:
- 共識觀點 → 合併相似表述，保留最完整版本
- 分歧觀點 → 並列呈現，標記分歧原因
- 創新想法 → 突出顯示，建立概念連接
```

### 🎯 MULTI_SOURCE_INTEGRATION_PROTOCOL
**META_PROCESS**: 你(AI)整合多源資料的標準流程
```
🔧 來源管理:
- 來源標記 → 每個觀點標明出處
- 權威性評估 → 學術>官方>專業>個人經驗
- 時效性標記 → 標注資料時間和有效期

🔧 內容統一:
- 術語統一 → 建立統一詞彙表
- 結構對齊 → 相同概念使用一致層級
- 風格協調 → 保持整體語調一致
```

## USER_SUPPLEMENTARY
// 用戶個人偏好設定區域
// 特定主題的標籤偏好和知識連接規則
// 個人化的前綴選擇和資訊整理策略
// 特殊格式要求和衝突處理偏好
// 討論串整理的個人化需求
// 多源整合的優先級設定
````
